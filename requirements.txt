# Data Pipeline Dependencies

# Database connectivity
PyMySQL==1.1.1
mysql-connector-python==8.4.0
SQLAlchemy==2.0.36

# Google Cloud libraries
google-cloud-storage==2.18.0
google-cloud-logging==3.11.0
google-cloud-monitoring==2.22.0

# Data processing
pandas==2.2.2
numpy==1.26.4

# Configuration and environment
python-dotenv==1.0.1
pyyaml==6.0.2

# SSH and networking
paramiko==3.5.0
fabric==3.2.2

# Utilities
requests==2.32.3
click==8.1.7
tqdm==4.66.5

# Logging and monitoring
structlog==24.4.0

# Testing (for development)
pytest==8.3.3
pytest-cov==5.0.0
mock==5.1.0