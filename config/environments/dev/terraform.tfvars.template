# Development Environment Configuration Template
# Copy this file to terraform.tfvars and fill in the actual values

# GCP Project Configuration
project_id = "external-data-source-437915"
project_name = "data-pipeline-dev"
#region = "us-central1"
#zone = "us-central1-a"
region = "europe-west10"
zone = "europe-west10-c"

# VM Configuration
machine_type = "e2-standard-2"  # Smaller instance for dev
vm_image = "ubuntu-os-cloud/ubuntu-2204-lts"
disk_size = 30  # Smaller disk for dev

# AWS EC2 SSH Configuration
aws_private_key = "-----BEGIN OPENSSH PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT_HERE\n-----END OPENSSH PRIVATE KEY-----"
aws_public_key = "ssh-rsa YOUR_PUBLIC_KEY_CONTENT_HERE"
aws_hostname = "***********"
aws_user = "forge"

# GitHub Repository Configuration
github_repo = "https://github.com/your-username/your-repo"
github_token = "ghp_your_github_token_here"

# Environment Settings
environment = "dev"
auto_delete_vm = true

# Pipeline Schedule (dev: run manually)
pipeline_schedule = "0 0 * * *"  # Daily at midnight for testing
