# Production Environment Configuration Template
# Copy this file to terraform.tfvars and fill in the actual values

# GCP Project Configuration
project_id = "external-data-source-437915"
project_name = "data-pipeline-prod"
#region = "us-central1"
#zone = "us-central1-a"
region = "europe-west10"
zone = "europe-west10-c"

# VM Configuration
machine_type = "e2-standard-4"  # Larger instance for production
vm_image = "ubuntu-os-cloud/ubuntu-2204-lts"
disk_size = 100  # Larger disk for production data

# AWS EC2 SSH Configuration
aws_private_key = "-----BEGIN OPENSSH PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT_HERE\n-----END OPENSSH PRIVATE KEY-----"
aws_public_key = "ssh-rsa YOUR_PUBLIC_KEY_CONTENT_HERE"
aws_hostname = "***********"
aws_user = "forge"

# GitHub Repository Configuration
github_repo = "https://github.com/your-username/your-repo"
github_token = "ghp_your_github_token_here"

# Environment Settings
environment = "prod"
auto_delete_vm = true

# Pipeline Schedule (production: weekly)
pipeline_schedule = "0 2 * * 0"  # Weekly on Sunday at 2 AM
