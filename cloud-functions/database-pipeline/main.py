"""
Database Pipeline Cloud Run Service
Handles SSH to AWS EC2, database dump, import, and validation query operations
"""

import os
import json
import logging
import requests
import tempfile
import subprocess
from datetime import datetime
from flask import Flask, request, jsonify
from google.cloud import logging as cloud_logging

# Setup logging
cloud_logging.Client().setup_logging()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class DatabasePipeline:
    def __init__(self):
        self.vm_manager_url = os.getenv('VM_MANAGER_URL')
        self.aws_hostname = os.getenv('AWS_HOSTNAME', '***********')
        self.aws_user = os.getenv('AWS_USER', 'forge')
        self.dump_file_path = "/tmp/forge_dump.sql"
        
    def execute_ssh_command(self, vm_name, ssh_command, description="SSH command"):
        """Execute SSH command on VM through VM manager"""
        try:
            logger.info(f"Executing {description} on VM {vm_name}")
            
            # Prepare the command to be executed on the VM
            full_command = f"sudo -u pipeline bash -c '{ssh_command}'"
            
            # Call VM manager to execute command
            response = requests.post(
                f"{self.vm_manager_url}/vm/{vm_name}/execute",
                json={"command": full_command, "user": "pipeline"},
                timeout=600  # 10 minutes timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if result["status"] == "success":
                    logger.info(f"{description} completed successfully")
                    return {"status": "success", "output": result["stdout"], "error": result["stderr"]}
                else:
                    logger.error(f"{description} failed: {result.get('stderr', 'Unknown error')}")
                    return {"status": "error", "message": result.get('stderr', 'Command failed')}
            else:
                logger.error(f"VM manager request failed: {response.status_code}")
                return {"status": "error", "message": f"VM manager request failed: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"{description} error: {e}")
            return {"status": "error", "message": str(e)}
    
    def test_ssh_connection(self, vm_name):
        """Test SSH connection to AWS EC2 from the VM"""
        logger.info("Testing SSH connection to AWS EC2...")
        
        ssh_command = f"""
        # Load SSH agent environment if available
        if [ -f ~/.ssh/load_ssh_agent.sh ]; then
            source ~/.ssh/load_ssh_agent.sh
        fi
        
        # Test SSH connection
        ssh -o ConnectTimeout=30 trips 'echo "SSH connection test successful"'
        
        # Test MySQL availability on remote host
        ssh trips 'mysql --version' || echo "MySQL not available but SSH works"
        """
        
        return self.execute_ssh_command(vm_name, ssh_command, "SSH connection test")
    
    def dump_forge_database(self, vm_name):
        """Execute SSH command to dump MySQL database from AWS EC2"""
        logger.info("Starting MySQL database dump from AWS EC2...")
        
        ssh_command = f"""
        # Create directory for dump file
        mkdir -p ~/Documents/cuba_buddy
        
        # Load SSH agent environment
        if [ -f ~/.ssh/load_ssh_agent.sh ]; then
            source ~/.ssh/load_ssh_agent.sh
        fi
        
        # Execute the database dump
        echo "Executing: ssh -TC trips 'mysqldump forge --ignore-table=forge.activity_log'"
        ssh -TC trips 'mysqldump forge --ignore-table=forge.activity_log' > ~/Documents/cuba_buddy/forge_dump.sql
        
        # Check if dump was successful
        if [ -f ~/Documents/cuba_buddy/forge_dump.sql ]; then
            DUMP_SIZE=$(wc -c < ~/Documents/cuba_buddy/forge_dump.sql)
            echo "Database dump completed successfully!"
            echo "Dump file size: $DUMP_SIZE bytes"
            echo "Dump file location: ~/Documents/cuba_buddy/forge_dump.sql"
            
            # Also create a copy in /tmp for processing
            cp ~/Documents/cuba_buddy/forge_dump.sql /tmp/forge_dump.sql
            echo "Temporary copy created at: /tmp/forge_dump.sql"
        else
            echo "ERROR: Dump file was not created"
            exit 1
        fi
        """
        
        return self.execute_ssh_command(vm_name, ssh_command, "Database dump")
    
    def import_forge_database(self, vm_name):
        """Import the forge_dump.sql file into local MySQL database"""
        logger.info("Importing forge database dump into local MySQL...")
        
        ssh_command = f"""
        # Create the forge database if it doesn't exist
        echo "Creating database 'forge' if it doesn't exist..."
        mysql -u pipeline -ppipeline123 -e "CREATE DATABASE IF NOT EXISTS forge;"
        
        if [ $? -eq 0 ]; then
            echo "Database 'forge' is ready"
        else
            echo "ERROR: Failed to create database"
            exit 1
        fi
        
        # Import the dump file into the forge database
        echo "Importing dump file: /tmp/forge_dump.sql"
        mysql -u pipeline -ppipeline123 forge < /tmp/forge_dump.sql
        
        if [ $? -eq 0 ]; then
            echo "Data imported successfully into 'forge' database"
            
            # Verify the import by checking tables
            echo "Verifying database import..."
            TABLES=$(mysql -u pipeline -ppipeline123 forge -e "SHOW TABLES;" 2>/dev/null | tail -n +2)
            
            if [ -n "$TABLES" ]; then
                echo "Database import verification successful"
                echo "Tables in 'forge' database:"
                echo "$TABLES"
                
                # Check if trips table exists
                if echo "$TABLES" | grep -q "trips"; then
                    echo "✓ 'trips' table found in the database"
                else
                    echo "⚠ 'trips' table not found in the database"
                fi
            else
                echo "⚠ No tables found in the database"
            fi
        else
            echo "ERROR: Data import failed"
            exit 1
        fi
        """
        
        return self.execute_ssh_command(vm_name, ssh_command, "Database import")
    
    def validate_database_query(self, vm_name):
        """Execute validation query on the trips table"""
        logger.info("Executing database validation query...")
        
        ssh_command = f"""
        # Execute the validation query
        QUERY="SELECT COUNT(*) FROM trips WHERE is_booker_version=1;"
        echo "Executing query: $QUERY"
        
        RESULT=$(mysql -u pipeline -ppipeline123 forge -e "$QUERY" 2>/dev/null | tail -n +2)
        
        if [ $? -eq 0 ]; then
            echo "DB Query output resulting in these rows: $RESULT"
            echo "VALIDATION_RESULT:$RESULT"  # Special marker for parsing
        else
            echo "ERROR: Database query failed"
            exit 1
        fi
        """
        
        return self.execute_ssh_command(vm_name, ssh_command, "Database validation query")
    
    def run_complete_pipeline(self, vm_name):
        """Run the complete database pipeline"""
        logger.info("=== Starting Complete Database Pipeline ===")
        
        pipeline_results = {
            "start_time": datetime.now().isoformat(),
            "steps": {},
            "final_result": None
        }
        
        try:
            # Step 1: Test SSH connection
            logger.info("--- Step 1: Testing SSH Connection ---")
            result = self.test_ssh_connection(vm_name)
            pipeline_results["steps"]["ssh_test"] = result
            
            if result["status"] != "success":
                logger.error("SSH connection test failed, aborting pipeline")
                return {"status": "error", "message": "SSH connection failed", "details": pipeline_results}
            
            # Step 2: Dump forge database from AWS EC2
            logger.info("--- Step 2: Dumping Database from AWS EC2 ---")
            result = self.dump_forge_database(vm_name)
            pipeline_results["steps"]["database_dump"] = result
            
            if result["status"] != "success":
                logger.error("Database dump failed, aborting pipeline")
                return {"status": "error", "message": "Database dump failed", "details": pipeline_results}
            
            # Step 3: Import database to local MySQL
            logger.info("--- Step 3: Importing Database to Local MySQL ---")
            result = self.import_forge_database(vm_name)
            pipeline_results["steps"]["database_import"] = result
            
            if result["status"] != "success":
                logger.error("Database import failed, aborting pipeline")
                return {"status": "error", "message": "Database import failed", "details": pipeline_results}
            
            # Step 4: Execute validation query
            logger.info("--- Step 4: Executing Validation Query ---")
            result = self.validate_database_query(vm_name)
            pipeline_results["steps"]["validation_query"] = result
            
            if result["status"] != "success":
                logger.error("Database validation failed")
                return {"status": "error", "message": "Database validation failed", "details": pipeline_results}
            
            # Extract validation result
            validation_output = result.get("output", "")
            validation_result = None
            for line in validation_output.split('\n'):
                if line.startswith("VALIDATION_RESULT:"):
                    validation_result = line.split(":", 1)[1].strip()
                    break
            
            pipeline_results["final_result"] = validation_result
            pipeline_results["end_time"] = datetime.now().isoformat()
            
            logger.info("=== Database Pipeline Completed Successfully ===")
            logger.info(f"Final validation result: {validation_result} rows found")
            
            return {
                "status": "success",
                "message": "Database pipeline completed successfully",
                "validation_result": validation_result,
                "details": pipeline_results
            }
            
        except Exception as e:
            logger.error(f"Pipeline execution error: {e}")
            pipeline_results["error"] = str(e)
            pipeline_results["end_time"] = datetime.now().isoformat()
            return {"status": "error", "message": str(e), "details": pipeline_results}

# Initialize database pipeline
db_pipeline = DatabasePipeline()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

@app.route('/pipeline/<vm_name>/ssh-test', methods=['POST'])
def test_ssh(vm_name):
    """Test SSH connection to AWS EC2"""
    try:
        result = db_pipeline.test_ssh_connection(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"SSH test endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/pipeline/<vm_name>/dump-database', methods=['POST'])
def dump_database(vm_name):
    """Dump database from AWS EC2"""
    try:
        result = db_pipeline.dump_forge_database(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Database dump endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/pipeline/<vm_name>/import-database', methods=['POST'])
def import_database(vm_name):
    """Import database to local MySQL"""
    try:
        result = db_pipeline.import_forge_database(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Database import endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/pipeline/<vm_name>/validate-query', methods=['POST'])
def validate_query(vm_name):
    """Execute validation query"""
    try:
        result = db_pipeline.validate_database_query(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Validation query endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/pipeline/<vm_name>/run-complete', methods=['POST'])
def run_complete_pipeline(vm_name):
    """Run the complete database pipeline"""
    try:
        result = db_pipeline.run_complete_pipeline(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Complete pipeline endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
