"""
VM Manager Cloud Run Service
Handles VM lifecycle management (create, start, stop, delete) and command execution
"""

import os
import json
import logging
import subprocess
import time
from datetime import datetime
from flask import Flask, request, jsonify
from google.cloud import compute_v1
from google.cloud import logging as cloud_logging

# Setup logging
cloud_logging.Client().setup_logging()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class VMManager:
    def __init__(self):
        self.project_id = os.getenv('GCP_PROJECT_ID', 'external-data-source-437915')
        self.zone = os.getenv('GCP_ZONE', 'europe-west3-a')
        self.compute_client = compute_v1.InstancesClient()
        
    def create_vm(self, vm_config):
        """Create a new VM instance"""
        try:
            logger.info(f"Creating VM: {vm_config['name']}")
            
            # VM configuration
            instance = compute_v1.Instance()
            instance.name = vm_config['name']
            instance.machine_type = f"zones/{self.zone}/machineTypes/{vm_config.get('machine_type', 'e2-medium')}"
            
            # Boot disk
            boot_disk = compute_v1.AttachedDisk()
            boot_disk.auto_delete = True
            boot_disk.boot = True
            boot_disk.initialize_params = compute_v1.AttachedDiskInitializeParams()
            boot_disk.initialize_params.disk_size_gb = vm_config.get('disk_size', 15)
            boot_disk.initialize_params.source_image = "projects/ubuntu-os-cloud/global/images/family/ubuntu-2204-lts"
            instance.disks = [boot_disk]
            
            # Network interface
            network_interface = compute_v1.NetworkInterface()
            network_interface.name = "default"
            access_config = compute_v1.AccessConfig()
            access_config.name = "External NAT"
            access_config.type_ = "ONE_TO_ONE_NAT"
            network_interface.access_configs = [access_config]
            instance.network_interfaces = [network_interface]
            
            # Metadata for startup script
            metadata = compute_v1.Metadata()
            startup_script = self._get_startup_script(vm_config)
            metadata.items = [
                compute_v1.Items(key="startup-script", value=startup_script),
                compute_v1.Items(key="enable-oslogin", value="TRUE")
            ]
            instance.metadata = metadata
            
            # Service account
            service_account = compute_v1.ServiceAccount()
            service_account.email = "default"
            service_account.scopes = [
                "https://www.googleapis.com/auth/cloud-platform"
            ]
            instance.service_accounts = [service_account]
            
            # Labels
            instance.labels = {
                "environment": vm_config.get('environment', 'dev'),
                "purpose": "data-pipeline",
                "auto-delete": "true"
            }
            
            # Create the instance
            operation = self.compute_client.insert(
                project=self.project_id,
                zone=self.zone,
                instance_resource=instance
            )
            
            logger.info(f"VM creation initiated: {operation.name}")
            return {"status": "success", "operation": operation.name, "vm_name": vm_config['name']}
            
        except Exception as e:
            logger.error(f"VM creation failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def start_vm(self, vm_name):
        """Start an existing VM"""
        try:
            logger.info(f"Starting VM: {vm_name}")
            operation = self.compute_client.start(
                project=self.project_id,
                zone=self.zone,
                instance=vm_name
            )
            return {"status": "success", "operation": operation.name}
        except Exception as e:
            logger.error(f"VM start failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def stop_vm(self, vm_name):
        """Stop a running VM"""
        try:
            logger.info(f"Stopping VM: {vm_name}")
            operation = self.compute_client.stop(
                project=self.project_id,
                zone=self.zone,
                instance=vm_name
            )
            return {"status": "success", "operation": operation.name}
        except Exception as e:
            logger.error(f"VM stop failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def delete_vm(self, vm_name):
        """Delete a VM instance"""
        try:
            logger.info(f"Deleting VM: {vm_name}")
            operation = self.compute_client.delete(
                project=self.project_id,
                zone=self.zone,
                instance=vm_name
            )
            return {"status": "success", "operation": operation.name}
        except Exception as e:
            logger.error(f"VM deletion failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def get_vm_status(self, vm_name):
        """Get VM status and information"""
        try:
            instance = self.compute_client.get(
                project=self.project_id,
                zone=self.zone,
                instance=vm_name
            )
            
            external_ip = None
            if instance.network_interfaces:
                for access_config in instance.network_interfaces[0].access_configs:
                    if access_config.name == "External NAT":
                        external_ip = access_config.nat_i_p
                        break
            
            return {
                "status": "success",
                "vm_status": instance.status,
                "external_ip": external_ip,
                "machine_type": instance.machine_type.split('/')[-1],
                "creation_timestamp": instance.creation_timestamp
            }
        except Exception as e:
            logger.error(f"Get VM status failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def wait_for_vm_ready(self, vm_name, timeout=600):
        """Wait for VM to be ready and startup script to complete"""
        try:
            logger.info(f"Waiting for VM {vm_name} to be ready...")
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                status_result = self.get_vm_status(vm_name)
                if status_result["status"] == "success" and status_result["vm_status"] == "RUNNING":
                    logger.info(f"VM {vm_name} is running")
                    
                    # Wait additional time for startup script
                    logger.info("Waiting for startup script to complete...")
                    time.sleep(120)  # 2 minutes for startup script
                    return {"status": "success", "message": "VM is ready"}
                
                logger.info(f"VM not ready yet, waiting... Status: {status_result.get('vm_status', 'unknown')}")
                time.sleep(30)
            
            return {"status": "error", "message": "VM readiness timeout"}
            
        except Exception as e:
            logger.error(f"Wait for VM ready failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def execute_command_on_vm(self, vm_name, command, user="pipeline"):
        """Execute a command on the VM via SSH"""
        try:
            logger.info(f"Executing command on VM {vm_name}: {command}")
            
            # Get VM external IP
            status_result = self.get_vm_status(vm_name)
            if status_result["status"] != "success":
                return status_result
            
            external_ip = status_result["external_ip"]
            if not external_ip:
                return {"status": "error", "message": "VM external IP not found"}
            
            # Execute SSH command
            ssh_command = [
                "gcloud", "compute", "ssh", f"{user}@{vm_name}",
                "--zone", self.zone,
                "--project", self.project_id,
                "--command", command,
                "--ssh-flag=-o StrictHostKeyChecking=no"
            ]
            
            result = subprocess.run(
                ssh_command,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            return {
                "status": "success" if result.returncode == 0 else "error",
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {"status": "error", "message": "Command execution timeout"}
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            return {"status": "error", "message": str(e)}
    
    def _get_startup_script(self, vm_config):
        """Generate startup script for VM"""
        return f"""#!/bin/bash
set -e

# Update system
apt-get update
apt-get install -y python3 python3-pip mysql-client git

# Create pipeline user
useradd -m -s /bin/bash pipeline
echo 'pipeline:pipeline123' | chpasswd
usermod -aG sudo pipeline

# Install MySQL server
apt-get install -y mysql-server
systemctl start mysql
systemctl enable mysql

# Configure MySQL
mysql -e "CREATE USER 'pipeline'@'localhost' IDENTIFIED BY 'pipeline123';"
mysql -e "GRANT ALL PRIVILEGES ON *.* TO 'pipeline'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# Setup SSH keys for pipeline user
sudo -u pipeline bash << 'EOF'
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Create SSH keys from metadata
cat > /home/<USER>/.ssh/aws_private_key << 'PRIVATE_KEY_EOF'
{vm_config.get('aws_private_key', '')}
PRIVATE_KEY_EOF

cat > /home/<USER>/.ssh/aws_public_key << 'PUBLIC_KEY_EOF'
{vm_config.get('aws_public_key', '')}
PUBLIC_KEY_EOF

chmod 600 /home/<USER>/.ssh/aws_private_key
chmod 644 /home/<USER>/.ssh/aws_public_key

# Create SSH config
cat > /home/<USER>/.ssh/config << 'SSH_CONFIG_EOF'
Host trips
    HostName {vm_config.get('aws_hostname', '***********')}
    User forge
    IdentityFile ~/.ssh/aws_private_key
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
SSH_CONFIG_EOF

chmod 600 /home/<USER>/.ssh/config

# Add GitHub to known hosts
ssh-keyscan -H github.com >> ~/.ssh/known_hosts

EOF

echo "VM setup completed successfully"
"""

# Initialize VM manager
vm_manager = VMManager()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

@app.route('/vm/create', methods=['POST'])
def create_vm():
    """Create a new VM"""
    try:
        vm_config = request.get_json()
        result = vm_manager.create_vm(vm_config)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Create VM endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/start', methods=['POST'])
def start_vm(vm_name):
    """Start a VM"""
    try:
        result = vm_manager.start_vm(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Start VM endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/stop', methods=['POST'])
def stop_vm(vm_name):
    """Stop a VM"""
    try:
        result = vm_manager.stop_vm(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Stop VM endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/delete', methods=['DELETE'])
def delete_vm(vm_name):
    """Delete a VM"""
    try:
        result = vm_manager.delete_vm(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Delete VM endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/status', methods=['GET'])
def get_vm_status(vm_name):
    """Get VM status"""
    try:
        result = vm_manager.get_vm_status(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Get VM status endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/wait-ready', methods=['POST'])
def wait_for_vm_ready(vm_name):
    """Wait for VM to be ready"""
    try:
        timeout = request.get_json().get('timeout', 600) if request.get_json() else 600
        result = vm_manager.wait_for_vm_ready(vm_name, timeout)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Wait for VM ready endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/execute', methods=['POST'])
def execute_command(vm_name):
    """Execute command on VM"""
    try:
        data = request.get_json()
        command = data.get('command')
        user = data.get('user', 'pipeline')
        
        if not command:
            return jsonify({"status": "error", "message": "Command is required"}), 400
        
        result = vm_manager.execute_command_on_vm(vm_name, command, user)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Execute command endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
