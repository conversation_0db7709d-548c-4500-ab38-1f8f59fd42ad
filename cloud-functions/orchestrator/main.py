"""
Pipeline Orchestrator Cloud Run Service
Coordinates the complete data pipeline workflow including VM management and cleanup
"""

import os
import json
import logging
import requests
import time
from datetime import datetime
from flask import Flask, request, jsonify
from google.cloud import logging as cloud_logging

# Setup logging
cloud_logging.Client().setup_logging()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class PipelineOrchestrator:
    def __init__(self):
        self.vm_manager_url = os.getenv('VM_MANAGER_URL')
        self.database_pipeline_url = os.getenv('DATABASE_PIPELINE_URL')
        self.project_id = os.getenv('GCP_PROJECT_ID', 'external-data-source-437915')
        
    def create_and_setup_vm(self, pipeline_config):
        """Create VM and wait for it to be ready"""
        try:
            logger.info("Creating and setting up VM...")
            
            vm_config = {
                "name": pipeline_config.get("vm_name", "cuba-buddy-data-pipeline-dev"),
                "machine_type": pipeline_config.get("machine_type", "e2-medium"),
                "disk_size": pipeline_config.get("disk_size", 15),
                "environment": pipeline_config.get("environment", "dev"),
                "aws_private_key": pipeline_config.get("aws_private_key"),
                "aws_public_key": pipeline_config.get("aws_public_key"),
                "aws_hostname": pipeline_config.get("aws_hostname", "***********")
            }
            
            # Create VM
            response = requests.post(
                f"{self.vm_manager_url}/vm/create",
                json=vm_config,
                timeout=300
            )
            
            if response.status_code != 200:
                return {"status": "error", "message": f"VM creation failed: {response.status_code}"}
            
            create_result = response.json()
            if create_result["status"] != "success":
                return create_result
            
            vm_name = vm_config["name"]
            logger.info(f"VM creation initiated: {vm_name}")
            
            # Wait for VM to be ready
            logger.info("Waiting for VM to be ready...")
            response = requests.post(
                f"{self.vm_manager_url}/vm/{vm_name}/wait-ready",
                json={"timeout": 600},
                timeout=700
            )
            
            if response.status_code != 200:
                return {"status": "error", "message": f"VM readiness check failed: {response.status_code}"}
            
            ready_result = response.json()
            if ready_result["status"] != "success":
                return ready_result
            
            logger.info(f"VM {vm_name} is ready")
            return {"status": "success", "vm_name": vm_name}
            
        except Exception as e:
            logger.error(f"VM setup error: {e}")
            return {"status": "error", "message": str(e)}
    
    def run_database_pipeline(self, vm_name):
        """Execute the complete database pipeline"""
        try:
            logger.info(f"Running database pipeline on VM: {vm_name}")
            
            response = requests.post(
                f"{self.database_pipeline_url}/pipeline/{vm_name}/run-complete",
                timeout=1800  # 30 minutes timeout
            )
            
            if response.status_code != 200:
                return {"status": "error", "message": f"Database pipeline failed: {response.status_code}"}
            
            result = response.json()
            logger.info(f"Database pipeline completed with status: {result['status']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Database pipeline error: {e}")
            return {"status": "error", "message": str(e)}
    
    def cleanup_vm(self, vm_name):
        """Delete the VM and cleanup resources"""
        try:
            logger.info(f"Cleaning up VM: {vm_name}")
            
            response = requests.delete(
                f"{self.vm_manager_url}/vm/{vm_name}/delete",
                timeout=300
            )
            
            if response.status_code != 200:
                logger.warning(f"VM deletion request failed: {response.status_code}")
                return {"status": "warning", "message": f"VM deletion request failed: {response.status_code}"}
            
            result = response.json()
            if result["status"] == "success":
                logger.info(f"VM {vm_name} deletion initiated")
                return {"status": "success", "message": "VM cleanup initiated"}
            else:
                logger.warning(f"VM deletion failed: {result.get('message', 'Unknown error')}")
                return result
            
        except Exception as e:
            logger.error(f"VM cleanup error: {e}")
            return {"status": "error", "message": str(e)}
    
    def run_complete_workflow(self, pipeline_config):
        """Run the complete data pipeline workflow"""
        logger.info("=== Starting Complete Data Pipeline Workflow ===")
        
        workflow_results = {
            "start_time": datetime.now().isoformat(),
            "steps": {},
            "vm_name": None,
            "final_result": None
        }
        
        vm_name = None
        
        try:
            # Step 1: Create and setup VM
            logger.info("--- Step 1: Creating and Setting Up VM ---")
            result = self.create_and_setup_vm(pipeline_config)
            workflow_results["steps"]["vm_setup"] = result
            
            if result["status"] != "success":
                logger.error("VM setup failed, aborting workflow")
                return {"status": "error", "message": "VM setup failed", "details": workflow_results}
            
            vm_name = result["vm_name"]
            workflow_results["vm_name"] = vm_name
            
            # Step 2: Run database pipeline
            logger.info("--- Step 2: Running Database Pipeline ---")
            result = self.run_database_pipeline(vm_name)
            workflow_results["steps"]["database_pipeline"] = result
            
            if result["status"] != "success":
                logger.error("Database pipeline failed")
                workflow_results["final_result"] = "Database pipeline failed"
            else:
                workflow_results["final_result"] = result.get("validation_result", "Unknown")
                logger.info(f"Database pipeline completed successfully. Validation result: {workflow_results['final_result']}")
            
            # Step 3: Cleanup (always attempt cleanup)
            logger.info("--- Step 3: Cleaning Up Resources ---")
            cleanup_result = self.cleanup_vm(vm_name)
            workflow_results["steps"]["cleanup"] = cleanup_result
            
            workflow_results["end_time"] = datetime.now().isoformat()
            
            if result["status"] == "success":
                logger.info("=== Complete Data Pipeline Workflow Completed Successfully ===")
                return {
                    "status": "success",
                    "message": "Complete workflow executed successfully",
                    "validation_result": workflow_results["final_result"],
                    "details": workflow_results
                }
            else:
                return {
                    "status": "error",
                    "message": "Database pipeline failed",
                    "details": workflow_results
                }
            
        except Exception as e:
            logger.error(f"Workflow execution error: {e}")
            workflow_results["error"] = str(e)
            workflow_results["end_time"] = datetime.now().isoformat()
            
            # Attempt cleanup if VM was created
            if vm_name:
                try:
                    logger.info("Attempting cleanup after error...")
                    cleanup_result = self.cleanup_vm(vm_name)
                    workflow_results["steps"]["cleanup"] = cleanup_result
                except Exception as cleanup_error:
                    logger.error(f"Cleanup after error failed: {cleanup_error}")
            
            return {"status": "error", "message": str(e), "details": workflow_results}
    
    def get_workflow_status(self, workflow_id):
        """Get status of a running workflow (placeholder for future implementation)"""
        # This could be implemented with a database or cache to track long-running workflows
        return {"status": "not_implemented", "message": "Workflow status tracking not implemented"}

# Initialize orchestrator
orchestrator = PipelineOrchestrator()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "timestamp": datetime.now().isoformat()})

@app.route('/workflow/run', methods=['POST'])
def run_workflow():
    """Run the complete data pipeline workflow"""
    try:
        pipeline_config = request.get_json()
        
        # Validate required configuration
        required_fields = ["aws_private_key", "aws_public_key"]
        for field in required_fields:
            if not pipeline_config.get(field):
                return jsonify({"status": "error", "message": f"Missing required field: {field}"}), 400
        
        result = orchestrator.run_complete_workflow(pipeline_config)
        
        # Return appropriate HTTP status code
        status_code = 200 if result["status"] == "success" else 500
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Workflow endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/workflow/<workflow_id>/status', methods=['GET'])
def get_workflow_status(workflow_id):
    """Get workflow status (placeholder)"""
    try:
        result = orchestrator.get_workflow_status(workflow_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Workflow status endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/create-and-setup', methods=['POST'])
def create_and_setup_vm():
    """Create and setup VM only"""
    try:
        pipeline_config = request.get_json()
        result = orchestrator.create_and_setup_vm(pipeline_config)
        return jsonify(result)
    except Exception as e:
        logger.error(f"VM setup endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/vm/<vm_name>/cleanup', methods=['DELETE'])
def cleanup_vm(vm_name):
    """Cleanup VM resources"""
    try:
        result = orchestrator.cleanup_vm(vm_name)
        return jsonify(result)
    except Exception as e:
        logger.error(f"VM cleanup endpoint error: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
