#!/bin/bash
"""
Database Pipeline Shell Script
Alternative shell script version of the database pipeline for direct execution
"""

set -e  # Exit on any error

# Configuration
DUMP_FILE="$HOME/Documents/cuba_buddy/forge_dump.sql"
TEMP_DUMP_FILE="/tmp/forge_dump.sql"
DB_USER="pipeline"
DB_PASSWORD="pipeline123"
FORGE_DB="forge"
SSH_HOST="trips"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

# Create cuba_buddy directory if it doesn't exist
mkdir -p "$(dirname "$DUMP_FILE")"

log "=== Starting Database Pipeline ==="
log "Pipeline Steps:"
log "1. Test SSH connection to GCP VM"
log "2. Dump 'forge' database (excluding activity_log table)"
log "3. Import dump to local MySQL 'forge' database"
log "4. Execute validation query on trips table"

# Step 1: Test SSH connection
log ""
log "--- Step 1: Testing SSH Connection ---"
if ssh -o ConnectTimeout=30 "$SSH_HOST" 'echo "SSH connection test successful"'; then
    log "✓ SSH connection successful"
    
    # Test MySQL availability on remote host
    if ssh "$SSH_HOST" 'mysql --version' >/dev/null 2>&1; then
        log "✓ MySQL available on remote host"
    else
        log "⚠ MySQL not available on remote host, but SSH connection works"
    fi
else
    log_error "SSH connection failed"
    exit 1
fi

# Step 2: Dump forge database from GCP VM
log ""
log "--- Step 2: Dumping Database from GCP VM ---"
log "Executing: ssh -TC $SSH_HOST 'mysqldump forge --ignore-table=forge.activity_log'"
log "Output will be saved to: $DUMP_FILE"

if ssh -TC "$SSH_HOST" 'mysqldump forge --ignore-table=forge.activity_log' > "$DUMP_FILE"; then
    DUMP_SIZE=$(wc -c < "$DUMP_FILE")
    log "✓ Database dump completed successfully!"
    log "Dump file size: $DUMP_SIZE bytes"
    log "Dump file location: $DUMP_FILE"
    
    # Create temporary copy for processing
    cp "$DUMP_FILE" "$TEMP_DUMP_FILE"
    log "Temporary copy created at: $TEMP_DUMP_FILE"
else
    log_error "Database dump failed"
    exit 1
fi

# Step 3: Import database to local MySQL
log ""
log "--- Step 3: Importing Database to Local MySQL ---"

# Create the forge database if it doesn't exist
log "Creating database '$FORGE_DB' if it doesn't exist..."
if mysql -u"$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $FORGE_DB;"; then
    log "✓ Database '$FORGE_DB' is ready"
else
    log_error "Failed to create database"
    exit 1
fi

# Import the dump file
log "Importing dump file: $TEMP_DUMP_FILE"
if mysql -u"$DB_USER" -p"$DB_PASSWORD" "$FORGE_DB" < "$TEMP_DUMP_FILE"; then
    log "✓ Data imported successfully into '$FORGE_DB' database"
    
    # Verify the import by checking tables
    log "Verifying database import..."
    TABLES=$(mysql -u"$DB_USER" -p"$DB_PASSWORD" "$FORGE_DB" -e "SHOW TABLES;" 2>/dev/null | tail -n +2)
    if [ -n "$TABLES" ]; then
        log "✓ Database import verification successful"
        log "Tables in '$FORGE_DB' database:"
        echo "$TABLES" | while read -r table; do
            log "  - $table"
        done
        
        # Check if trips table exists
        if echo "$TABLES" | grep -q "trips"; then
            log "✓ 'trips' table found in the database"
        else
            log "⚠ 'trips' table not found in the database"
        fi
    else
        log "⚠ No tables found in the database"
    fi
else
    log_error "Data import failed"
    exit 1
fi

# Step 4: Execute validation query
log ""
log "--- Step 4: Executing Validation Query ---"
QUERY="SELECT COUNT(*) FROM trips WHERE is_booker_version=1;"
log "Executing query: $QUERY"

if RESULT=$(mysql -u"$DB_USER" -p"$DB_PASSWORD" "$FORGE_DB" -e "$QUERY" 2>/dev/null | tail -n +2); then
    log "DB Query output resulting in these rows: $RESULT"
    echo "DB Query output resulting in these rows: $RESULT"
else
    log_error "Database query failed"
    exit 1
fi

# Cleanup
log ""
log "--- Cleanup ---"
if [ -f "$TEMP_DUMP_FILE" ]; then
    rm -f "$TEMP_DUMP_FILE"
    log "✓ Temporary files cleaned up"
fi

# Success
END_TIME=$(date)
log ""
log "=== Database Pipeline Completed Successfully ==="
log "Completion time: $END_TIME"
log "Final validation result: $RESULT rows found"

exit 0
