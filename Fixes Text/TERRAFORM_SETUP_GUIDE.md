# Terraform Setup Guide - Fixing GitHub Actions Pipeline Errors

## Issues Fixed

### 1. Invalid Multi-line String Format
**Problem**: The `aws_private_key` variable was using invalid multi-line string syntax in `terraform.tfvars`.

**Error**: 
```
Error: Invalid multi-line string
Quoted strings may not be split over multiple lines.
```

**Solution**: Use Terraform's heredoc syntax for multi-line strings:

```hcl
# ❌ WRONG - Don't use regular quotes for multi-line content
aws_private_key = "-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_KEY_CONTENT
-----END OPENSSH PRIVATE KEY-----"

# ✅ CORRECT - Use heredoc syntax
aws_private_key = <<-EOT
-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_KEY_CONTENT
-----END OPENSSH PRIVATE KEY-----
EOT
```

### 2. Variable Name Mismatch
**Problem**: The templatefile call was passing `project_repo` but the startup script expected `github_repo`.

**Error**:
```
Invalid value for "vars" parameter: vars map does not contain key "github_repo"
```

**Solution**: Fixed the variable name in `main.tf` from `project_repo` to `github_repo`.

## Setup Instructions for GitHub Actions

### Step 1: Create terraform.tfvars for Local Development

1. Copy the example file:
   ```bash
   cp infrastructure/terraform/terraform.tfvars.example infrastructure/terraform/terraform.tfvars
   ```

2. Fill in your actual values using the correct format:

```hcl
# GCP Configuration
project_id   = "your-actual-project-id"
project_name = "data-pipeline"
region       = "us-central1"
zone         = "us-central1-a"

# VM Configuration
machine_type = "e2-standard-4"
vm_image     = "ubuntu-os-cloud/ubuntu-2204-lts"
disk_size    = 50

# AWS Configuration
aws_hostname = "your-aws-ec2-ip"
aws_user     = "your-aws-username"

# AWS Private Key - Use heredoc format
aws_private_key = <<-EOT
-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_ACTUAL_PRIVATE_KEY_CONTENT_HERE
PASTE_ALL_LINES_OF_YOUR_PRIVATE_KEY
-----END OPENSSH PRIVATE KEY-----
EOT

# AWS Public Key - Use heredoc format
aws_public_key = <<-EOT
ssh-rsa YOUR_ACTUAL_PUBLIC_KEY_CONTENT user@hostname
EOT

# GitHub Configuration
github_repo  = "https://github.com/your-username/your-repo-name"
github_token = "ghp_your_github_personal_access_token"

# Environment Configuration
environment      = "dev"
auto_delete_vm   = true
pipeline_schedule = "0 2 * * 0"
```

### Step 2: Configure GitHub Secrets for CI/CD

For your GitHub Actions pipeline, you'll need to set up repository secrets instead of using a terraform.tfvars file:

1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add the following repository secrets:

```
TF_VAR_project_id = "your-gcp-project-id"
TF_VAR_aws_hostname = "your-aws-ec2-ip"
TF_VAR_aws_user = "your-aws-username"
TF_VAR_aws_private_key = "-----BEGIN OPENSSH PRIVATE KEY-----\nYOUR_KEY_CONTENT\n-----END OPENSSH PRIVATE KEY-----"
TF_VAR_aws_public_key = "ssh-rsa YOUR_PUBLIC_KEY_CONTENT user@hostname"
TF_VAR_github_repo = "https://github.com/your-username/your-repo-name"
TF_VAR_github_token = "ghp_your_github_token"
```

**Important**: For multi-line secrets like SSH keys, use `\n` to represent newlines in the GitHub secret value.

### Step 3: Update GitHub Actions Workflow

Make sure your GitHub Actions workflow file includes the environment variables:

```yaml
- name: Terraform Plan
  env:
    TF_VAR_project_id: ${{ secrets.TF_VAR_project_id }}
    TF_VAR_aws_hostname: ${{ secrets.TF_VAR_aws_hostname }}
    TF_VAR_aws_user: ${{ secrets.TF_VAR_aws_user }}
    TF_VAR_aws_private_key: ${{ secrets.TF_VAR_aws_private_key }}
    TF_VAR_aws_public_key: ${{ secrets.TF_VAR_aws_public_key }}
    TF_VAR_github_repo: ${{ secrets.TF_VAR_github_repo }}
    TF_VAR_github_token: ${{ secrets.TF_VAR_github_token }}
  run: terraform plan
```

## Key Points to Remember

1. **Never commit terraform.tfvars** - It's already in .gitignore
2. **Use heredoc syntax** for multi-line strings in .tfvars files
3. **Use GitHub Secrets** for CI/CD pipelines
4. **Use \n for newlines** in GitHub secret values for multi-line content
5. **Variable names must match** between main.tf templatefile calls and the startup script

## Testing the Fix

1. Run `terraform fmt -check` locally to verify syntax
2. Run `terraform validate` to check configuration
3. Test the GitHub Actions pipeline

The errors you encountered should now be resolved!
