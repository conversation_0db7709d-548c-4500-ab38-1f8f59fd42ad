# Complete Production Deployment Guide

## 🚨 **CRITICAL FIX: Boolean Logic Error**

### **Issue Found**:
The `create-vm` job was being skipped due to incorrect boolean logic in GitHub Actions:

**Problem**:
```yaml
!github.event.inputs.skip_vm_creation  # This evaluates to FALSE when skip_vm_creation is "false"
```

**Fix**:
```yaml
github.event.inputs.skip_vm_creation != 'true'  # This correctly evaluates to TRUE
```

### **Root Cause**:
GitHub Actions treats boolean inputs as strings, so:
- Input: `skip_vm_creation: false` 
- GitHub stores as: `"false"` (string)
- Expression `!"false"` = `false` (because non-empty string is truthy)
- Expression `"false" != 'true'` = `true` (correct!)

## ✅ **Fix Applied**

### **Updated Job Conditions**:
1. **create-vm**: `github.event.inputs.skip_vm_creation != 'true'`
2. **run-pipeline**: `github.event.inputs.skip_vm_creation == 'true'` (for existing VM)
3. **cleanup-vm**: `github.event.inputs.skip_vm_creation != 'true'`
4. **Debug output**: Fixed to show correct evaluation

## 🚀 **Production Deployment Process**

### **Prerequisites**:

#### **1. GitHub Secrets Setup**:
Ensure all required secrets are configured in your repository:

**GCP Secrets**:
- `GCP_SA_KEY` - Service account JSON key
- `GCP_PROJECT_ID` - Your GCP project ID
- `GCP_REGION` - e.g., `us-central1`
- `GCP_ZONE` - e.g., `us-central1-a`

**VM Configuration**:
- `VM_MACHINE_TYPE` - e.g., `e2-standard-4`

**AWS Connection**:
- `AWS_HOSTNAME` - Your AWS EC2 IP address
- `AWS_USER` - SSH username (e.g., `forge`, `ubuntu`)
- `AWS_PRIVATE_KEY` - Complete SSH private key content
- `AWS_PUBLIC_KEY` - Complete SSH public key content

#### **2. Service Account Permissions**:
Ensure `vm-cuba-buddy-data-ingestion` has these roles:
- ✅ Cloud Run developer
- ✅ Cloud Scheduler Admin
- ✅ Compute Admin
- ✅ Compute Storage Admin
- ✅ Logging Admin
- ✅ Monitoring Admin
- ✅ Storage Admin
- ✅ **Service Account User** (CRITICAL - add this if missing)

#### **3. AWS EC2 Setup**:
- ✅ EC2 instance running and accessible
- ✅ Security group allows SSH (port 22) from GCP IP ranges
- ✅ SSH keys properly configured
- ✅ MySQL/MariaDB installed and running (if needed)

### **Production Deployment Steps**:

#### **Step 1: Push the Fixed Code**
```bash
git add .
git commit -m "Fix boolean logic in workflow conditions"
git push origin main
```

#### **Step 2: Test in Development First**
1. **Go to GitHub Actions**
2. **Click "Run Data Pipeline"**
3. **Select**:
   - Environment: `dev`
   - Skip VM creation: `false`
   - Pipeline action: `test-ssh-only`
4. **Click "Run workflow"**
5. **Verify**: SSH connection works and VM is cleaned up

#### **Step 3: Deploy to Production**
1. **Go to GitHub Actions**
2. **Click "Run Data Pipeline"**
3. **Select**:
   - Environment: `prod`
   - Skip VM creation: `false`
   - Pipeline action: `run-with-cleanup`
4. **Click "Run workflow"**

### **Expected Production Flow**:

#### **Phase 1: Job Execution Check**
```
=== Job Execution Conditions ===
Event: workflow_dispatch
Environment: prod
Skip VM Creation: false
Pipeline Action: run-with-cleanup

=== Job Should Run Checks ===
Should create VM: true  ✅ (Fixed!)
Should run pipeline: true
```

#### **Phase 2: VM Creation**
```
=== Create Pipeline VM ===
Creating terraform.tfvars...
Terraform Init...
Terraform Apply...
VM Name: data-pipeline-prod-pipeline-vm
VM Zone: us-central1-a
Waiting for VM startup to complete...
VM startup completed successfully!
```

#### **Phase 3: Pipeline Execution**
```
=== VM Information Debug ===
Using newly created VM: data-pipeline-prod-pipeline-vm in us-central1-a
Transferring script to VM...
Executing pipeline script on VM...

Starting data pipeline execution...
Testing SSH connection to AWS EC2...
SSH connection verified: [timestamp]
SSH connection successful!

[Your pipeline logic here]
Pipeline execution completed at: [timestamp]
```

#### **Phase 4: Log Collection**
```
=== Pipeline Startup Log ===
[Complete startup script output]

=== SSH Test Results ===
SSH Connection Test Started...
SSH connection successful!
System Information: [AWS system details]
Hostname: [AWS hostname]
MySQL/MariaDB Status: active (running)
Available Databases: [database list]
```

#### **Phase 5: Automatic Cleanup**
```
=== Cleanup Pipeline VM ===
Destroying infrastructure...
Terraform destroy completed
Destroy complete! Resources: 7 destroyed.
```

### **Production Monitoring**:

#### **Success Indicators**:
- ✅ All 5 phases complete successfully
- ✅ SSH connection test shows AWS system info
- ✅ Pipeline logic executes without errors
- ✅ All resources are destroyed (cost = $0)
- ✅ Total execution time: 10-30 minutes
- ✅ Cost per run: ~$0.15-0.50

#### **Failure Points to Monitor**:
- ❌ VM creation fails → Check Terraform/GCP permissions
- ❌ SSH connection fails → Check AWS security groups/keys
- ❌ Pipeline fails → Check your pipeline logic/dependencies
- ❌ Cleanup fails → Manually destroy resources to avoid costs

### **Production Best Practices**:

#### **Scheduling**:
The workflow includes automatic scheduling:
```yaml
schedule:
  - cron: '0 2 * * 0'  # Weekly on Sunday at 2 AM UTC
```

#### **Environment Management**:
- **dev**: For testing and development
- **staging**: For pre-production validation  
- **prod**: For production data pipeline runs

#### **Cost Control**:
- ✅ Always use `run-with-cleanup` in production
- ✅ Monitor GCP billing for unexpected charges
- ✅ Set up billing alerts in GCP Console
- ✅ Use `test-ssh-only` for connection testing

#### **Security**:
- ✅ Rotate SSH keys regularly
- ✅ Use least-privilege service account permissions
- ✅ Monitor access logs in GCP Console
- ✅ Keep GitHub secrets updated

### **Troubleshooting Production Issues**:

#### **If VM Creation Fails**:
1. Check GCP quotas and limits
2. Verify service account permissions
3. Check Terraform state bucket access
4. Ensure all GitHub secrets are correct

#### **If SSH Connection Fails**:
1. Verify AWS EC2 instance is running
2. Check AWS security group rules
3. Validate SSH keys in GitHub secrets
4. Test SSH connection manually from GCP Console

#### **If Pipeline Logic Fails**:
1. Check your pipeline scripts for errors
2. Verify database connections and credentials
3. Monitor VM logs for detailed error messages
4. Use `run-keep-vm` option for debugging

## 🎯 **Next Steps**

1. **Push the boolean logic fix**
2. **Test in dev environment first**
3. **Deploy to production**
4. **Monitor the complete execution**
5. **Set up regular scheduling if needed**

The boolean logic fix will resolve the VM creation issue, and you'll finally see your complete pipeline execution with SSH test results!
