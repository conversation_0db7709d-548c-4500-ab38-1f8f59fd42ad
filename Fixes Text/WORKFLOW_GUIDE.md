# Complete GitHub Actions Workflow Guide

## 🚨 **FIXED: Automatic Deployment Issue**

### **Problem Solved**:
- ✅ **Disabled automatic deployment** on push to main
- ✅ **Enhanced Run Data Pipeline** workflow with manual trigger
- ✅ **Added destroy option** for easy cleanup
- ✅ **Prevented accidental resource creation**

## 🎯 **Available Workflows**

### **1. Deploy Data Pipeline Infrastructure**
**Purpose**: Infrastructure management (persistent resources)
**Trigger**: Manual only (workflow_dispatch)
**Use when**: Setting up/managing infrastructure

**Options**:
- `plan` - Preview changes
- `apply` - Create/update infrastructure  
- `destroy` - Remove all infrastructure
- `force-apply` - Force apply changes
- `clean-deploy` - Destroy then recreate

### **2. Run Data Pipeline** ⭐ **RECOMMENDED**
**Purpose**: Execute pipeline with automatic cleanup
**Trigger**: Manual (workflow_dispatch) + Scheduled (weekly)
**Use when**: Running your data pipeline

**New Options**:
- `run-with-cleanup` - Full pipeline + auto destroy (DEFAULT)
- `run-keep-vm` - Full pipeline but keep VM running
- `test-ssh-only` - Test SSH connection only + cleanup
- `destroy-existing` - Just destroy existing resources

## 🚀 **How to Use the Workflows**

### **STEP 1: Destroy Current Resources** (URGENT)

Your resources are still running! Clean them up:

1. **Go to GitHub Actions**
2. **Click "Run Data Pipeline"**
3. **Click "Run workflow"**
4. **Select**:
   - Branch: `main`
   - Environment: `dev`
   - Skip VM creation: `false`
   - Pipeline action: `destroy-existing`
5. **Click "Run workflow"**

### **STEP 2: Add Missing IAM Role**

1. **GCP Console** → **IAM & Admin** → **IAM**
2. **Find**: `vm-cuba-buddy-data-ingestion@[PROJECT].iam.gserviceaccount.com`
3. **Edit Principal** → **Add Role**: `Service Account User`
4. **Save**

### **STEP 3: Run Your Pipeline Correctly**

1. **Go to GitHub Actions**
2. **Click "Run Data Pipeline"**
3. **Click "Run workflow"**
4. **Select**:
   - Branch: `main`
   - Environment: `dev`
   - Skip VM creation: `false`
   - Pipeline action: `run-with-cleanup`
5. **Click "Run workflow"**

## 📋 **Workflow Options Explained**

### **Run Data Pipeline Options**:

#### `run-with-cleanup` (Recommended)
- ✅ Creates VM
- ✅ Runs startup script with SSH test
- ✅ Executes pipeline
- ✅ Shows all logs including SSH results
- ✅ **Destroys everything automatically**
- 💰 **Cost**: ~$0.15 for 30 minutes

#### `run-keep-vm`
- ✅ Creates VM
- ✅ Runs pipeline
- ❌ **Keeps VM running** (for debugging)
- ⚠️ **Cost**: ~$3.60/day until manually destroyed

#### `test-ssh-only`
- ✅ Creates VM
- ✅ Tests SSH connection to AWS
- ✅ Shows SSH test results
- ✅ Destroys VM
- 💰 **Cost**: ~$0.05 for 10 minutes

#### `destroy-existing`
- ✅ Destroys any existing resources
- ✅ No VM creation
- 💰 **Cost**: $0

## 🔍 **Expected Output with Correct Workflow**

### **Phase 1: VM Creation**
```
=== Create Pipeline VM ===
VM Name: data-pipeline-dev-pipeline-vm
VM Zone: us-central1-a
Project ID: your-project-id

=== Waiting for VM Startup ===
Checking startup progress... (attempt 1/60)
VM startup completed successfully!
```

### **Phase 2: Startup Logs with SSH Test**
```
=== VM Startup Logs ===
Data Pipeline VM Startup Script Started...
Testing SSH connection to AWS EC2...
SSH connection to AWS EC2 successful!
System Information: Linux ip-172-31-x-x 5.4.0-aws
Disk Usage: /dev/xvda1 20G 8.1G 11G 43% /
Memory Usage: total used free shared
MySQL/MariaDB Status: active (running)
Data Pipeline VM Startup Script Completed Successfully
```

### **Phase 3: Pipeline Execution**
```
=== Execute Data Pipeline ===
Testing SSH connection to AWS EC2...
SSH connection verified: Mon Jan 20 10:30:00 UTC 2025
Running Python pipeline...
Basic pipeline test completed successfully
Pipeline execution completed
```

### **Phase 4: Log Collection**
```
=== Pipeline Startup Log ===
[Full startup script output]

=== SSH Test Results ===
SSH Connection Test Started...
SSH connection successful!
AWS EC2 system information gathered successfully
```

### **Phase 5: Automatic Cleanup**
```
=== Cleanup Pipeline VM ===
Destroying infrastructure...
Destroy complete! Resources: 7 destroyed.
```

## 🛠️ **Troubleshooting**

### **"Run workflow" button not showing**:
- Make sure you're on the main branch
- Refresh the GitHub Actions page
- Check if the workflow file is in `.github/workflows/`

### **SSH connection fails**:
- Verify AWS EC2 instance is running
- Check AWS security groups allow SSH (port 22)
- Verify SSH keys are correct in GitHub secrets

### **Pipeline execution fails**:
- Check VM startup logs for errors
- Verify all GitHub secrets are set correctly
- Ensure service account has required permissions

## 📊 **Cost Optimization**

### **Always Use**:
- `run-with-cleanup` for production pipelines
- `test-ssh-only` for testing connections
- `destroy-existing` to clean up resources

### **Avoid**:
- `run-keep-vm` unless debugging
- Leaving resources running after testing
- Using infrastructure workflow for pipeline execution

## 🔄 **Workflow Decision Tree**

```
Need to run data pipeline?
├─ Yes → Use "Run Data Pipeline" 
│   ├─ First time/testing → "test-ssh-only"
│   ├─ Production run → "run-with-cleanup"
│   └─ Debugging needed → "run-keep-vm"
└─ No → Need to manage infrastructure?
    ├─ Yes → Use "Deploy Data Pipeline Infrastructure"
    │   ├─ Clean up → "destroy"
    │   ├─ Set up → "apply"
    │   └─ Fresh start → "clean-deploy"
    └─ No → Clean up existing resources
        └─ Use "Run Data Pipeline" → "destroy-existing"
```

## ⚠️ **Important Reminders**

1. **Always destroy resources** when not in use
2. **Monitor GCP billing** regularly
3. **Use correct workflow** for your use case
4. **Check logs thoroughly** for SSH connection results
5. **Verify cleanup** after each run
