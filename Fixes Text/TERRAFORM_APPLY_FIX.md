# Terraform Apply Not Running - Complete Fix

## 🔍 **Problem Analysis**

### Issue Description:
- ✅ **Terraform Plan**: Runs successfully and detects 14 resources to create
- ❌ **Terraform Apply**: Job is skipped and never runs
- 🔄 **Expected**: Apply should run automatically after successful plan on main branch

### Root Causes Identified:

#### 1. **Exit Code Output Issue**
The terraform plan step was capturing the exit code correctly but always exiting with `0`, preventing the apply job condition from working.

#### 2. **String vs Number Comparison**
The apply job condition was comparing exit code as number (`== 2`) instead of string (`== '2'`).

#### 3. **Resource Naming Issue**
Double environment names in resources (e.g., `data-pipeline-dev-dev-network`) due to environment being added twice.

## ✅ **Complete Solution Implemented**

### 1. **Fixed Terraform Plan Exit Code Handling**

**Before** (lines 147-158):
```yaml
run: |
  export exitcode=0
  terraform plan -detailed-exitcode -no-color -out tfplan || export exitcode=$?
  
  echo "exitcode=$exitcode" >> $GITHUB_OUTPUT
  
  if [ $exitcode -eq 1 ]; then
    echo Terraform Plan Failed!
    exit 1
  else 
    exit 0  # ❌ Always exits with 0
  fi
```

**After** (lines 147-163):
```yaml
run: |
  export exitcode=0
  terraform plan -detailed-exitcode -no-color -out tfplan || export exitcode=$?
  
  echo "exitcode=$exitcode" >> $GITHUB_OUTPUT
  echo "Terraform plan exit code: $exitcode"
  
  if [ $exitcode -eq 1 ]; then
    echo "Terraform Plan Failed!"
    exit 1
  elif [ $exitcode -eq 2 ]; then
    echo "Terraform Plan detected changes - will trigger apply"
    exit 0
  else
    echo "Terraform Plan completed - no changes detected"
    exit 0
  fi
```

### 2. **Fixed Apply Job Condition**

**Before**:
```yaml
if: |
  (github.ref == 'refs/heads/main' && needs.terraform-plan.outputs.tfplanExitCode == 2) ||
```

**After**:
```yaml
if: |
  (github.ref == 'refs/heads/main' && needs.terraform-plan.outputs.tfplanExitCode == '2') ||
```

**Key Change**: Exit code comparison as string (`'2'`) instead of number (`2`).

### 3. **Added Debug Information**

Added debug step to show condition evaluation:
```yaml
- name: Debug Apply Condition
  run: |
    echo "=== Terraform Apply Job Condition Debug ==="
    echo "github.ref: ${{ github.ref }}"
    echo "terraform plan exit code: ${{ needs.terraform-plan.outputs.tfplanExitCode }}"
    echo "github.event.inputs.action: ${{ github.event.inputs.action }}"
    echo "Condition evaluation:"
    echo "  - Push to main with changes: ${{ github.ref == 'refs/heads/main' && needs.terraform-plan.outputs.tfplanExitCode == '2' }}"
    echo "  - Manual apply action: ${{ github.event.inputs.action == 'apply' }}"
    echo "  - Manual destroy action: ${{ github.event.inputs.action == 'destroy' }}"
    echo "=== End Debug ==="
```

### 4. **Fixed Resource Naming**

**Before**: `TF_VAR_project_name: "data-pipeline-dev"`
**After**: `TF_VAR_project_name: "data-pipeline"`

**Result**: 
- ❌ Before: `data-pipeline-dev-dev-network` (double environment)
- ✅ After: `data-pipeline-dev-network` (correct naming)

## 🎯 **How the Apply Trigger Works**

### Terraform Plan Exit Codes:
- **0**: No changes detected (apply won't run)
- **1**: Error occurred (apply won't run, plan fails)
- **2**: Changes detected (apply will run)

### Apply Job Conditions:
1. **Automatic (Push to main)**: `github.ref == 'refs/heads/main' && tfplanExitCode == '2'`
2. **Manual Apply**: `github.event.inputs.action == 'apply'`
3. **Manual Destroy**: `github.event.inputs.action == 'destroy'`

## 🚀 **Expected Results After Fix**

### When you push to main branch:

#### ✅ **Terraform Plan Job**:
```
Terraform plan exit code: 2
Terraform Plan detected changes - will trigger apply
Plan: 14 to add, 0 to change, 0 to destroy
```

#### ✅ **Terraform Apply Job**:
```
=== Terraform Apply Job Condition Debug ===
github.ref: refs/heads/main
terraform plan exit code: 2
github.event.inputs.action: 
Condition evaluation:
  - Push to main with changes: true
  - Manual apply action: false
  - Manual destroy action: false
=== End Debug ===

[Apply job runs and creates resources]
```

### Resource Names (Fixed):
- **VM**: `data-pipeline-dev-pipeline-vm`
- **Network**: `data-pipeline-dev-network`
- **Subnet**: `data-pipeline-dev-subnet`
- **Service Account**: `data-pipeline-dev-sa`
- **Firewall Rules**: `data-pipeline-dev-allow-ssh`, `data-pipeline-dev-allow-internal`

## 📋 **Deployment Instructions**

### 1. **Commit and Push Changes**:
```bash
git add .
git commit -m "Fix terraform apply not running - fix exit code handling and conditions"
git push origin main
```

### 2. **Monitor Pipeline**:
- Go to GitHub Actions
- Watch the "Deploy Data Pipeline Infrastructure" workflow
- **Plan job** should complete with exit code 2
- **Apply job** should start automatically and create resources

### 3. **Alternative: Manual Trigger**:
If you want to test manually:
1. Go to Actions → "Deploy Data Pipeline Infrastructure"
2. Click "Run workflow"
3. Select:
   - **Environment**: `dev`
   - **Action**: `apply`
4. Click "Run workflow"

## 🔧 **Verification Steps**

### After Successful Deployment:

1. **Check GCP Console**:
   - **Compute Engine**: VM should be created and running
   - **VPC Network**: Network and subnet should exist
   - **IAM**: Service account should be created with proper roles
   - **Cloud Storage**: Bucket should be created

2. **Check GitHub Actions Logs**:
   - Plan job shows exit code 2
   - Apply job runs and completes successfully
   - VM information is displayed at the end

3. **Test SSH Connection**:
   ```bash
   gcloud compute ssh data-pipeline-dev-pipeline-vm --zone=us-central1-a --project=external-data-source-437915
   ```

## 🎉 **Summary**

The terraform apply job should now run automatically when:
- ✅ **Push to main branch** with infrastructure changes
- ✅ **Manual workflow dispatch** with apply action
- ✅ **Proper exit code handling** preserves terraform plan results
- ✅ **Fixed resource naming** eliminates double environment names
- ✅ **Debug information** helps troubleshoot future issues

Your infrastructure deployment should now complete successfully! 🚀
