# Cloud Run Architecture for Data Pipeline

This document describes the new Cloud Run-based architecture that orchestrates VM operations and database pipeline execution through serverless functions.

## Architecture Overview

The new architecture consists of three Cloud Run services that work together to execute the complete data pipeline:

```
GitHub Actions → Orchestrator → VM Manager + Database Pipeline → AWS EC2
                     ↓
                 VM Creation/Management
                     ↓
                Database Operations
                     ↓
                 Cleanup & Results
```

## Cloud Run Services

### 1. VM Manager Service (`vm-manager`)
**Purpose**: Handles VM lifecycle management and command execution

**Endpoints**:
- `POST /vm/create` - Create a new VM instance
- `POST /vm/{vm_name}/start` - Start an existing VM
- `POST /vm/{vm_name}/stop` - Stop a running VM
- `DELETE /vm/{vm_name}/delete` - Delete a VM instance
- `GET /vm/{vm_name}/status` - Get VM status and information
- `POST /vm/{vm_name}/wait-ready` - Wait for VM to be ready
- `POST /vm/{vm_name}/execute` - Execute command on VM via SSH

**Key Features**:
- Automatic VM creation with proper SSH configuration
- SSH key management for AWS EC2 connections
- Command execution through gcloud compute ssh
- VM status monitoring and readiness checks

### 2. Database Pipeline Service (`database-pipeline`)
**Purpose**: Handles database operations (dump, import, validation)

**Endpoints**:
- `POST /pipeline/{vm_name}/ssh-test` - Test SSH connection to AWS EC2
- `POST /pipeline/{vm_name}/dump-database` - Dump database from AWS EC2
- `POST /pipeline/{vm_name}/import-database` - Import database to local MySQL
- `POST /pipeline/{vm_name}/validate-query` - Execute validation query
- `POST /pipeline/{vm_name}/run-complete` - Run complete database pipeline

**Key Features**:
- SSH connection testing to AWS EC2
- MySQL database dump with specific exclusions
- Local database import and verification
- Validation query execution with result parsing

### 3. Orchestrator Service (`orchestrator`)
**Purpose**: Coordinates the complete workflow and manages cleanup

**Endpoints**:
- `POST /workflow/run` - Run complete data pipeline workflow
- `GET /workflow/{workflow_id}/status` - Get workflow status (placeholder)
- `POST /vm/create-and-setup` - Create and setup VM only
- `DELETE /vm/{vm_name}/cleanup` - Cleanup VM resources

**Key Features**:
- Complete workflow orchestration
- VM creation and readiness management
- Database pipeline execution coordination
- Automatic cleanup after completion or failure

## Workflow Execution

### Complete Workflow Steps

1. **Infrastructure Deployment**
   - Deploy Cloud Run services via GitHub Actions
   - Build and push container images to Artifact Registry
   - Update service configurations with environment variables

2. **Pipeline Execution**
   - Orchestrator receives pipeline configuration
   - Creates VM with SSH keys and database setup
   - Waits for VM to be ready (startup script completion)
   - Executes database pipeline through Database Pipeline service
   - Collects results and performs cleanup

3. **Database Pipeline Steps**
   - Test SSH connection to AWS EC2
   - Execute: `ssh -TC trips 'mysqldump forge --ignore-table=forge.activity_log'`
   - Save dump to `~/Documents/cuba_buddy/forge_dump.sql`
   - Import dump to local MySQL 'forge' database
   - Execute validation query: `SELECT COUNT(*) FROM trips WHERE is_booker_version=1`
   - Return results in format: "DB Query output resulting in these rows: [count]"

4. **Cleanup**
   - Delete VM instance
   - Clean up temporary resources
   - Return final results

## GitHub Actions Integration

### New Workflow: `run-cloud-pipeline.yml`

**Trigger Options**:
- `run-complete-workflow` - Full pipeline execution (default)
- `deploy-cloud-run-only` - Deploy services only
- `test-services-only` - Test existing services
- `run-pipeline-only` - Run pipeline with existing services

**Environment Variables**:
- `GCP_PROJECT_ID` - Google Cloud project ID
- `GCP_REGION` - Deployment region (europe-west3)
- Required secrets: `GCP_SA_KEY`, `AWS_PRIVATE_KEY`, `AWS_PUBLIC_KEY`, `AWS_HOSTNAME`

## Deployment Methods

### Method 1: GitHub Actions (Recommended)
```bash
# Go to GitHub Actions → Run Cloud Data Pipeline
# Select: run-complete-workflow
# Environment: dev/staging/prod
# Deploy Cloud Run: true
```

### Method 2: Local Deployment
```bash
# Set environment variables
export GCP_PROJECT_ID="external-data-source-437915"
export GCP_REGION="europe-west3"

# Deploy everything
./scripts/deploy-cloud-run.sh full

# Or deploy step by step
./scripts/deploy-cloud-run.sh check
./scripts/deploy-cloud-run.sh build
./scripts/deploy-cloud-run.sh deploy
./scripts/deploy-cloud-run.sh test
```

### Method 3: Manual API Calls
```bash
# Get orchestrator URL
ORCHESTRATOR_URL=$(gcloud run services describe pipeline-orchestrator --region=europe-west3 --format="value(status.url)")

# Execute pipeline
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "vm_name": "cuba-buddy-data-pipeline-dev",
    "machine_type": "e2-medium",
    "disk_size": 15,
    "environment": "dev",
    "aws_private_key": "your-private-key",
    "aws_public_key": "your-public-key",
    "aws_hostname": "***********"
  }' \
  "$ORCHESTRATOR_URL/workflow/run"
```

## Benefits of Cloud Run Architecture

### 1. **Cost Efficiency**
- Pay only when services are running
- Automatic scaling to zero when not in use
- No idle VM costs

### 2. **Scalability**
- Automatic scaling based on demand
- Concurrent pipeline executions
- No resource management overhead

### 3. **Reliability**
- Built-in health checks and monitoring
- Automatic retries and error handling
- Centralized logging and observability

### 4. **Maintainability**
- Modular service architecture
- Independent service updates
- Clear separation of concerns

### 5. **Security**
- Managed service accounts and IAM
- Secure inter-service communication
- No exposed VM endpoints

## Monitoring and Logging

### Cloud Logging
All services use Google Cloud Logging for centralized log collection:
```bash
# View logs for specific service
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=vm-manager"
```

### Service Health Checks
Each service provides a `/health` endpoint:
```bash
curl https://vm-manager-xxx-ew.a.run.app/health
curl https://database-pipeline-xxx-ew.a.run.app/health
curl https://pipeline-orchestrator-xxx-ew.a.run.app/health
```

### Monitoring Dashboard
Access Cloud Run metrics in Google Cloud Console:
- Request count and latency
- Error rates and status codes
- Resource utilization
- Cold start metrics

## Troubleshooting

### Common Issues

1. **Service Not Responding**
   ```bash
   # Check service status
   gcloud run services describe vm-manager --region=europe-west3
   
   # View recent logs
   gcloud logs read "resource.type=cloud_run_revision" --limit=50
   ```

2. **VM Creation Failures**
   ```bash
   # Check VM Manager logs
   gcloud logs read "resource.labels.service_name=vm-manager" --limit=20
   
   # Verify IAM permissions
   gcloud projects get-iam-policy external-data-source-437915
   ```

3. **Database Pipeline Errors**
   ```bash
   # Check Database Pipeline logs
   gcloud logs read "resource.labels.service_name=database-pipeline" --limit=20
   
   # Test SSH connectivity manually
   curl -X POST https://database-pipeline-xxx/pipeline/test-vm/ssh-test
   ```

### Debug Mode
Enable detailed logging by setting environment variables:
```bash
gcloud run services update vm-manager \
  --set-env-vars="LOG_LEVEL=DEBUG" \
  --region=europe-west3
```

## Migration from VM-based Pipeline

### Key Changes
1. **Execution Model**: From direct VM execution to Cloud Run orchestration
2. **SSH Method**: Fixed GitHub repo cloning with SSH keys
3. **Scalability**: Multiple concurrent pipeline executions
4. **Cost Model**: Pay-per-use instead of always-on VMs

### Migration Steps
1. Deploy Cloud Run services alongside existing infrastructure
2. Test Cloud Run pipeline with existing data
3. Update GitHub Actions to use new workflow
4. Gradually migrate scheduled jobs to Cloud Run
5. Decommission old VM-based pipeline

## Next Steps

After successful deployment:

1. **Test the complete workflow**:
   ```bash
   # Run via GitHub Actions
   # Go to Actions → Run Cloud Data Pipeline → run-complete-workflow
   ```

2. **Monitor execution**:
   - Check Cloud Run logs for detailed execution traces
   - Verify database dump file creation
   - Confirm validation query results

3. **Set up monitoring**:
   - Configure alerting for pipeline failures
   - Set up dashboard for pipeline metrics
   - Monitor cost and resource usage

4. **Scale as needed**:
   - Adjust service resource limits
   - Configure concurrent execution limits
   - Optimize container images for faster cold starts

This Cloud Run architecture provides a robust, scalable, and cost-effective solution for the data pipeline while maintaining all the original functionality and requirements.
