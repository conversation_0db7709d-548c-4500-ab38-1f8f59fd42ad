# Complete Setup Guide for Cloud Run Data Pipeline

This guide provides step-by-step instructions for setting up and running the Cloud Run data pipeline both locally and via GitHub Actions.

## Cloud Run Architecture

**Answer to your question**: This project uses **Cloud Run (2nd generation)**, not Cloud Functions. Cloud Run is a fully managed serverless platform that runs containerized applications, while Cloud Functions are for event-driven functions. We chose Cloud Run because:

- Better for long-running processes (database operations)
- More control over the runtime environment
- Better for HTTP-based microservices architecture
- Supports larger memory and CPU allocations
- More suitable for our multi-step pipeline workflow

## Prerequisites

### Required Tools

1. **Docker Desktop**
   ```bash
   # macOS
   brew install --cask docker
   
   # Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install docker.io
   
   # Or download from: https://docs.docker.com/get-docker/
   ```

2. **Google Cloud SDK**
   ```bash
   # macOS
   brew install google-cloud-sdk
   
   # Ubuntu/Debian
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   
   # Or visit: https://cloud.google.com/sdk/docs/install
   ```

3. **Terraform**
   ```bash
   # macOS
   brew install terraform
   
   # Ubuntu/Debian
   wget -O- https://apt.releases.hashicorp.com/gpg | sudo gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
   echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
   sudo apt update && sudo apt install terraform
   ```

### Required Environment Variables

Create a `.env` file in your project root:

```bash
# .env file
export PROJECT_ID="external-data-source-437915"
export REGION="europe-west3"
export ZONE="europe-west3-a"

# AWS Configuration (your remote database server)
export AWS_HOSTNAME="***********"
export AWS_PRIVATE_KEY="-----BEGIN OPENSSH PRIVATE KEY-----
your-private-key-content-here
-----END OPENSSH PRIVATE KEY-----"
export AWS_PUBLIC_KEY="ssh-rsa your-public-key-content-here user@hostname"

# GitHub Configuration
export GITHUB_TOKEN="ghp_your_github_personal_access_token"
```

Load the environment variables:
```bash
source .env
```

## Local Development Setup

### Step 1: Initial Setup

1. **Clone and navigate to the repository**:
   ```bash
   cd /Users/<USER>/Documents/Project/gcp_data_pipeline_tools_ingestion
   ```

2. **Authenticate with Google Cloud**:
   ```bash
   gcloud auth login
   gcloud config set project external-data-source-437915
   gcloud auth application-default login
   ```

3. **Start Docker Desktop**:
   - On macOS: Open Docker Desktop application
   - On Linux: `sudo systemctl start docker`

4. **Verify prerequisites**:
   ```bash
   ./scripts/deploy-cloud-run.sh check
   ```

### Step 2: Deploy Cloud Run Services Locally

1. **Full deployment** (recommended for first time):
   ```bash
   ./scripts/deploy-cloud-run.sh full
   ```

2. **Step-by-step deployment** (for debugging):
   ```bash
   # Check prerequisites
   ./scripts/deploy-cloud-run.sh check
   
   # Build and push container images
   ./scripts/deploy-cloud-run.sh build
   
   # Deploy infrastructure
   ./scripts/deploy-cloud-run.sh deploy
   
   # Test services
   ./scripts/deploy-cloud-run.sh test
   
   # Get service URLs
   ./scripts/deploy-cloud-run.sh urls
   ```

### Step 3: Test the Pipeline Locally

1. **Get the orchestrator URL**:
   ```bash
   ORCHESTRATOR_URL=$(gcloud run services describe pipeline-orchestrator --region=europe-west3 --format="value(status.url)")
   echo "Orchestrator URL: $ORCHESTRATOR_URL"
   ```

2. **Execute the complete pipeline**:
   ```bash
   curl -X POST \
     -H "Content-Type: application/json" \
     -d "{
       \"vm_name\": \"cuba-buddy-data-pipeline-dev\",
       \"machine_type\": \"e2-medium\",
       \"disk_size\": 15,
       \"environment\": \"dev\",
       \"aws_private_key\": \"$AWS_PRIVATE_KEY\",
       \"aws_public_key\": \"$AWS_PUBLIC_KEY\",
       \"aws_hostname\": \"$AWS_HOSTNAME\"
     }" \
     "$ORCHESTRATOR_URL/workflow/run"
   ```

3. **Monitor the execution**:
   ```bash
   # View Cloud Run logs
   gcloud logs read "resource.type=cloud_run_revision" --limit=50
   
   # View specific service logs
   gcloud logs read "resource.labels.service_name=pipeline-orchestrator" --limit=20
   ```

### Step 4: Expected Local Results

When successful, you should see:
```json
{
  "status": "success",
  "message": "Complete workflow executed successfully",
  "validation_result": "42",
  "details": {
    "start_time": "2025-06-27T11:30:00.000Z",
    "steps": {
      "vm_setup": {"status": "success"},
      "database_pipeline": {"status": "success"},
      "cleanup": {"status": "success"}
    },
    "final_result": "42",
    "end_time": "2025-06-27T11:35:00.000Z"
  }
}
```

## GitHub Actions Setup

### Step 1: Push Code to GitHub

1. **Commit your changes**:
   ```bash
   git add .
   git commit -m "Implement Cloud Run architecture for data pipeline"
   ```

2. **Push to different branches**:
   ```bash
   # For production
   git push origin main
   
   # For testing
   git checkout -b testing
   git push origin testing
   
   # For development
   git checkout -b production
   git push origin production
   ```

### Step 2: Configure GitHub Secrets

Ensure these secrets are set in your GitHub repository:

1. Go to **Settings** → **Secrets and variables** → **Actions**
2. Add these repository secrets:
   - `GCP_SA_KEY` - Your service account JSON key
   - `AWS_PRIVATE_KEY` - Your AWS EC2 private key
   - `AWS_PUBLIC_KEY` - Your AWS EC2 public key
   - `AWS_HOSTNAME` - Your AWS EC2 IP address (***********)

### Step 3: Run GitHub Actions Workflow

#### Automatic Triggers
The workflow automatically runs when you push to:
- `main` branch (production)
- `production` branch (production)
- `testing` branch (testing environment)

#### Manual Triggers
1. Go to **Actions** → **Run Cloud Data Pipeline**
2. Click **Run workflow**
3. Select:
   - **Branch**: `main`, `testing`, or `production`
   - **Environment**: `dev`, `staging`, or `prod`
   - **Pipeline action**: `run-complete-workflow`
   - **Deploy Cloud Run**: `true`

#### Available Pipeline Actions
- `run-complete-workflow` - Deploy services and run complete pipeline
- `deploy-cloud-run-only` - Only deploy/update Cloud Run services
- `test-services-only` - Test existing services without deployment
- `run-pipeline-only` - Run pipeline with existing services

### Step 4: Monitor GitHub Actions Execution

1. **View workflow logs** in the Actions tab
2. **Check for successful deployment** of Cloud Run services
3. **Look for the final output**: "DB Query output resulting in these rows: [count]"
4. **Verify cleanup** was performed automatically

## Troubleshooting

### Common Local Issues

1. **Docker not running**:
   ```bash
   # Start Docker Desktop or
   sudo systemctl start docker
   ```

2. **Authentication issues**:
   ```bash
   gcloud auth login
   gcloud auth application-default login
   ```

3. **Permission denied**:
   ```bash
   chmod +x scripts/deploy-cloud-run.sh
   ```

4. **Terraform state issues**:
   ```bash
   cd infrastructure/terraform
   terraform init -reconfigure
   ```

### Common GitHub Actions Issues

1. **Service account permissions**: Ensure your GCP service account has:
   - Cloud Run Admin
   - Compute Admin
   - Storage Admin
   - Artifact Registry Admin

2. **Secret format**: Ensure AWS_PRIVATE_KEY includes the full key with headers:
   ```
   -----BEGIN OPENSSH PRIVATE KEY-----
   key-content-here
   -----END OPENSSH PRIVATE KEY-----
   ```

3. **Branch protection**: Ensure the workflow has permission to run on your target branches

## Development Workflow

### Daily Development Cycle

1. **Make changes** to Cloud Run services or infrastructure
2. **Test locally**:
   ```bash
   ./scripts/deploy-cloud-run.sh full
   ```
3. **Commit and push**:
   ```bash
   git add .
   git commit -m "Your changes"
   git push origin testing  # Test in testing branch first
   ```
4. **Verify in GitHub Actions** that the testing branch works
5. **Merge to main** when ready for production

### Cost Management

- **Local development**: ~$5-10 per day of active development
- **GitHub Actions**: ~$2-5 per workflow run
- **Production**: ~$10-20 per month for scheduled runs

### Service URLs

After deployment, your services will be available at:
- VM Manager: `https://vm-manager-xxx-ew.a.run.app`
- Database Pipeline: `https://database-pipeline-xxx-ew.a.run.app`
- Orchestrator: `https://pipeline-orchestrator-xxx-ew.a.run.app`

Use `./scripts/deploy-cloud-run.sh urls` to get the exact URLs.

## Next Steps

1. **Test locally** with the complete setup
2. **Push to testing branch** and verify GitHub Actions
3. **Push to main branch** for production deployment
4. **Set up monitoring** and alerting for production use
5. **Schedule regular pipeline runs** via GitHub Actions cron

This setup provides a robust, scalable, and cost-effective solution for your data pipeline needs!
