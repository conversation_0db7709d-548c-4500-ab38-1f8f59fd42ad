# GitHub Secrets Setup Guide

This guide explains how to set up all required GitHub Secrets for the data pipeline deployment.

## 🔐 Required Secrets

Navigate to your GitHub repository → **Settings** → **Secrets and variables** → **Actions** → **New repository secret**

### 1. GCP_SA_KEY
**Description**: Google Cloud Service Account key in JSON format

**Service Account**: `<EMAIL>`

**How to get it:**
```bash
# Create and download key for existing service account
gcloud iam service-accounts keys create vm-cuba-buddy-key.json \
  --iam-account=<EMAIL> \
  --project=external-data-source-437915

# Copy the entire content of vm-cuba-buddy-key.json
cat vm-cuba-buddy-key.json
```

**Value**: Paste the entire JSON content (including curly braces)

### 2. GCP_PROJECT_ID
**Description**: Your Google Cloud Project ID

**Value**: `external-data-source-437915`

### 3. GCP_REGION
**Description**: GCP region where resources will be created

**Value**: `us-central1` (or your preferred region)

### 4. GCP_ZONE
**Description**: GCP zone where the VM will be created

**Value**: `us-central1-a` (or your preferred zone)

### 5. AWS_PRIVATE_KEY
**Description**: Private key content for SSH access to AWS EC2

**How to get it:**
```bash
# If you have the private key file
cat your-aws-key.pem

# Or if you need to create a new key pair
ssh-keygen -t rsa -b 4096 -f aws_ec2_key
cat aws_ec2_key
```

**Value**: 
```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAlwAAAAdzc2gtcn
[... rest of your private key content ...]
-----END OPENSSH PRIVATE KEY-----
```

**⚠️ Important**: 
- Include the full key including header and footer lines
- Ensure proper line breaks are maintained
- Never share this key publicly

### 6. AWS_PUBLIC_KEY
**Description**: Public key content corresponding to the private key

**How to get it:**
```bash
# If you have the private key, generate public key
ssh-keygen -y -f your-aws-key.pem

# Or if you created a new key pair
cat aws_ec2_key.pub
```

**Value**: 
```
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDExample... user@hostname
```

### 7. AWS_HOSTNAME
**Description**: IP address or hostname of your AWS EC2 instance

**How to get it:**
- From AWS Console: EC2 → Instances → Select your instance → Public IPv4 address
- Or use AWS CLI: `aws ec2 describe-instances`

**Value**: `***********` (replace with your actual IP)

### 8. AWS_USER
**Description**: Username for SSH access to AWS EC2

**Common values:**
- Ubuntu instances: `ubuntu`
- Amazon Linux: `ec2-user`
- CentOS: `centos`
- Custom: `forge` (as in your example)

**Value**: `forge` (or your actual username)

## 🔧 Optional Secrets

### VM_MACHINE_TYPE
**Description**: GCP VM machine type for the pipeline

**Default**: `e2-standard-4`

**Other options:**
- `e2-standard-2` (for smaller workloads)
- `e2-standard-8` (for larger workloads)
- `n1-standard-4` (for specific requirements)

## 🧪 Testing Your Setup

After setting up all secrets, you can test the configuration:

1. **Test Infrastructure Deployment:**
   - Go to Actions → Deploy Data Pipeline Infrastructure
   - Run workflow with "plan" action
   - Check for any configuration errors

2. **Test SSH Connection:**
   - Deploy the infrastructure with "apply" action
   - Check VM startup logs for SSH test results
   - Look for "SSH connection successful" message

## 🔍 Verification Checklist

Before running the pipeline, verify:

- [ ] All 8 required secrets are set
- [ ] GCP service account has necessary permissions
- [ ] AWS EC2 instance is running and accessible
- [ ] SSH key pair is correctly configured
- [ ] AWS security groups allow SSH from GCP IP ranges
- [ ] GCP APIs are enabled (Compute Engine, Storage, Logging)

## 🚨 Security Notes

1. **Rotate secrets regularly** - especially SSH keys and service account keys
2. **Use least privilege principle** - only grant necessary permissions
3. **Monitor secret usage** - check GitHub Actions logs for any issues
4. **Never log sensitive data** - secrets are automatically masked in logs
5. **Review access regularly** - remove unused secrets and permissions

## 🔧 Troubleshooting

### Common Issues:

1. **Invalid JSON in GCP_SA_KEY**
   - Ensure the entire JSON is copied including `{` and `}`
   - Check for any missing characters or line breaks

2. **SSH Connection Failed**
   - Verify AWS_HOSTNAME is correct and instance is running
   - Check AWS security groups allow SSH (port 22)
   - Ensure private key format is correct

3. **Permission Denied**
   - Verify GCP service account has Editor role
   - Check if required GCP APIs are enabled

4. **Terraform Backend Issues**
   - Ensure GCP_PROJECT_ID is correct
   - Verify service account has Storage Admin permissions

### Debug Commands:

```bash
# Test GCP authentication locally
gcloud auth activate-service-account --key-file=terraform-sa-key.json
gcloud projects list

# Test SSH key locally
ssh -i your-private-key.pem user@hostname

# Validate Terraform configuration
terraform validate
terraform plan
```

## 📞 Getting Help

If you encounter issues:
1. Check the main setup guide: `docs/setup/README.md`
2. Review GitHub Actions workflow logs
3. Check GCP Cloud Logging for detailed error messages
4. Verify all prerequisites are met
