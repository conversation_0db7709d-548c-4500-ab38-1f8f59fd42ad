#!/bin/bash
#
# Install Prerequisites Script
# Installs required tools for the Cloud Run data pipeline
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    log "Detected OS: $OS"
}

# Install Homebrew on macOS
install_homebrew() {
    if ! command -v brew &> /dev/null; then
        log "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    else
        log "✓ Homebrew already installed"
    fi
}

# Install Docker
install_docker() {
    log "Installing Docker..."
    
    if command -v docker &> /dev/null; then
        log "✓ Docker already installed"
        return
    fi
    
    case $OS in
        "macos")
            install_homebrew
            brew install --cask docker
            log "Docker installed. Please start Docker Desktop manually."
            ;;
        "linux")
            # Update package index
            sudo apt-get update
            
            # Install Docker
            sudo apt-get install -y docker.io
            
            # Add user to docker group
            sudo usermod -aG docker $USER
            
            # Start and enable Docker
            sudo systemctl start docker
            sudo systemctl enable docker
            
            log "Docker installed. Please log out and log back in for group changes to take effect."
            ;;
    esac
}

# Install Google Cloud SDK
install_gcloud() {
    log "Installing Google Cloud SDK..."
    
    if command -v gcloud &> /dev/null; then
        log "✓ Google Cloud SDK already installed"
        return
    fi
    
    case $OS in
        "macos")
            install_homebrew
            brew install google-cloud-sdk
            ;;
        "linux")
            # Add the Cloud SDK distribution URI as a package source
            echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
            
            # Import the Google Cloud Platform public key
            curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
            
            # Update the package list and install the Cloud SDK
            sudo apt-get update && sudo apt-get install -y google-cloud-sdk
            ;;
    esac
}

# Install Terraform
install_terraform() {
    log "Installing Terraform..."
    
    if command -v terraform &> /dev/null; then
        log "✓ Terraform already installed"
        return
    fi
    
    case $OS in
        "macos")
            install_homebrew
            brew install terraform
            ;;
        "linux")
            # Add HashiCorp GPG key
            wget -O- https://apt.releases.hashicorp.com/gpg | sudo gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
            
            # Add HashiCorp repository
            echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
            
            # Update and install Terraform
            sudo apt-get update && sudo apt-get install -y terraform
            ;;
    esac
}

# Install curl and jq
install_utilities() {
    log "Installing utilities (curl, jq)..."
    
    case $OS in
        "macos")
            install_homebrew
            brew install curl jq
            ;;
        "linux")
            sudo apt-get update
            sudo apt-get install -y curl jq
            ;;
    esac
}

# Verify installations
verify_installations() {
    log "Verifying installations..."
    
    local all_good=true
    
    # Check Docker
    if command -v docker &> /dev/null; then
        log "✓ Docker: $(docker --version)"
    else
        log_error "✗ Docker not found"
        all_good=false
    fi
    
    # Check Google Cloud SDK
    if command -v gcloud &> /dev/null; then
        log "✓ Google Cloud SDK: $(gcloud --version | head -n1)"
    else
        log_error "✗ Google Cloud SDK not found"
        all_good=false
    fi
    
    # Check Terraform
    if command -v terraform &> /dev/null; then
        log "✓ Terraform: $(terraform --version | head -n1)"
    else
        log_error "✗ Terraform not found"
        all_good=false
    fi
    
    # Check curl
    if command -v curl &> /dev/null; then
        log "✓ curl: $(curl --version | head -n1)"
    else
        log_error "✗ curl not found"
        all_good=false
    fi
    
    # Check jq
    if command -v jq &> /dev/null; then
        log "✓ jq: $(jq --version)"
    else
        log_error "✗ jq not found"
        all_good=false
    fi
    
    if [ "$all_good" = true ]; then
        log "All prerequisites installed successfully!"
    else
        log_error "Some prerequisites failed to install"
        exit 1
    fi
}

# Setup authentication
setup_authentication() {
    log "Setting up authentication..."
    
    # Check if already authenticated
    if gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log "✓ Already authenticated with Google Cloud"
    else
        log "Please authenticate with Google Cloud:"
        gcloud auth login
        gcloud auth application-default login
    fi
    
    # Set project
    gcloud config set project external-data-source-437915
    log "✓ Project set to external-data-source-437915"
}

# Create environment file template
create_env_template() {
    log "Creating environment file template..."
    
    if [ ! -f ".env.template" ]; then
        cat > .env.template << 'EOF'
# Environment Variables Template
# Copy this to .env and fill in your values

export PROJECT_ID="external-data-source-437915"
export REGION="europe-west3"
export ZONE="europe-west3-a"

# AWS Configuration (your remote database server)
export AWS_HOSTNAME="***********"
export AWS_PRIVATE_KEY="-----BEGIN OPENSSH PRIVATE KEY-----
your-private-key-content-here
-----END OPENSSH PRIVATE KEY-----"
export AWS_PUBLIC_KEY="ssh-rsa your-public-key-content-here user@hostname"

# GitHub Configuration
export GITHUB_TOKEN="ghp_your_github_personal_access_token"
EOF
        log "✓ Created .env.template file"
        log "Please copy .env.template to .env and fill in your values"
    else
        log "✓ .env.template already exists"
    fi
}

# Main installation function
main() {
    log "=== Installing Prerequisites for Cloud Run Data Pipeline ==="
    
    detect_os
    install_docker
    install_gcloud
    install_terraform
    install_utilities
    verify_installations
    setup_authentication
    create_env_template
    
    log "=== Prerequisites Installation Completed ==="
    log ""
    log "Next steps:"
    log "1. Copy .env.template to .env and fill in your values"
    log "2. Source the environment: source .env"
    log "3. Start Docker Desktop (if on macOS)"
    log "4. Run: ./scripts/deploy-cloud-run.sh check"
    log "5. Run: ./scripts/deploy-cloud-run.sh full"
}

# Run main function
main "$@"
