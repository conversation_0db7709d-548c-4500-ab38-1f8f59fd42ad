#!/bin/bash
#
# Deploy Cloud Run Services Script
# Builds and deploys all Cloud Run services for the data pipeline
#

set -e

# Configuration
PROJECT_ID=${PROJECT_ID:-"external-data-source-437915"}
REGION=${REGION:-"europe-west3"}
ZONE=${ZONE:-"europe-west3-a"}
REPOSITORY="pipeline-services"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check if authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud. Run: gcloud auth login"
        exit 1
    fi
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        log_error "Please install Docker:"
        log_error "  macOS: brew install --cask docker"
        log_error "  Ubuntu: sudo apt-get install docker.io"
        log_error "  Or visit: https://docs.docker.com/get-docker/"
        exit 1
    fi

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        log_error "Please start Docker Desktop or run: sudo systemctl start docker"
        exit 1
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    
    log "Prerequisites check passed"
}

# Configure Docker for Artifact Registry
configure_docker() {
    log "Configuring Docker for Artifact Registry..."
    gcloud auth configure-docker ${REGION}-docker.pkg.dev --quiet
}

# Build and push a service
build_and_push_service() {
    local service_name=$1
    local service_dir="cloud-functions/$service_name"
    
    log "Building and pushing $service_name..."
    
    if [ ! -d "$service_dir" ]; then
        log_error "Service directory not found: $service_dir"
        return 1
    fi
    
    # Build the Docker image
    docker build -t ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${service_name}:latest $service_dir
    
    # Push the image
    docker push ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${service_name}:latest
    
    log "$service_name built and pushed successfully"
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    log "Deploying infrastructure with Terraform..."
    
    cd infrastructure/terraform
    
    # Initialize Terraform
    terraform init
    
    # Plan the deployment
    log "Planning Terraform deployment..."
    terraform plan -out=tfplan
    
    # Apply the deployment
    log "Applying Terraform deployment..."
    terraform apply tfplan
    
    cd ../..
    
    log "Infrastructure deployed successfully"
}

# Update Cloud Run services
update_cloud_run_services() {
    log "Updating Cloud Run services..."
    
    # Get the repository URL
    REPO_URL="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}"
    
    # Update VM Manager service
    log "Updating VM Manager service..."
    gcloud run deploy vm-manager \
        --image=${REPO_URL}/vm-manager:latest \
        --region=${REGION} \
        --platform=managed \
        --allow-unauthenticated \
        --set-env-vars="GCP_PROJECT_ID=${PROJECT_ID},GCP_ZONE=${REGION}-a" \
        --memory=2Gi \
        --cpu=2 \
        --timeout=3600 \
        --max-instances=10 \
        --quiet
    
    # Get VM Manager URL
    VM_MANAGER_URL=$(gcloud run services describe vm-manager --region=${REGION} --format="value(status.url)")
    
    # Update Database Pipeline service
    log "Updating Database Pipeline service..."
    gcloud run deploy database-pipeline \
        --image=${REPO_URL}/database-pipeline:latest \
        --region=${REGION} \
        --platform=managed \
        --allow-unauthenticated \
        --set-env-vars="VM_MANAGER_URL=${VM_MANAGER_URL},AWS_HOSTNAME=***********,AWS_USER=forge" \
        --memory=1Gi \
        --cpu=1 \
        --timeout=3600 \
        --max-instances=5 \
        --quiet
    
    # Get Database Pipeline URL
    DATABASE_PIPELINE_URL=$(gcloud run services describe database-pipeline --region=${REGION} --format="value(status.url)")
    
    # Update Orchestrator service
    log "Updating Orchestrator service..."
    gcloud run deploy pipeline-orchestrator \
        --image=${REPO_URL}/orchestrator:latest \
        --region=${REGION} \
        --platform=managed \
        --allow-unauthenticated \
        --set-env-vars="VM_MANAGER_URL=${VM_MANAGER_URL},DATABASE_PIPELINE_URL=${DATABASE_PIPELINE_URL},GCP_PROJECT_ID=${PROJECT_ID}" \
        --memory=1Gi \
        --cpu=1 \
        --timeout=3600 \
        --max-instances=3 \
        --quiet
    
    log "Cloud Run services updated successfully"
}

# Get service URLs
get_service_urls() {
    log "Getting service URLs..."
    
    VM_MANAGER_URL=$(gcloud run services describe vm-manager --region=${REGION} --format="value(status.url)")
    DATABASE_PIPELINE_URL=$(gcloud run services describe database-pipeline --region=${REGION} --format="value(status.url)")
    ORCHESTRATOR_URL=$(gcloud run services describe pipeline-orchestrator --region=${REGION} --format="value(status.url)")
    
    echo ""
    log "=== Cloud Run Service URLs ==="
    echo "VM Manager:        $VM_MANAGER_URL"
    echo "Database Pipeline: $DATABASE_PIPELINE_URL"
    echo "Orchestrator:      $ORCHESTRATOR_URL"
    echo ""
    
    # Save URLs to file for GitHub Actions
    cat > cloud-run-urls.txt << EOF
VM_MANAGER_URL=$VM_MANAGER_URL
DATABASE_PIPELINE_URL=$DATABASE_PIPELINE_URL
ORCHESTRATOR_URL=$ORCHESTRATOR_URL
EOF
    
    log "Service URLs saved to cloud-run-urls.txt"
}

# Test services
test_services() {
    log "Testing Cloud Run services..."
    
    # Test VM Manager health
    VM_MANAGER_URL=$(gcloud run services describe vm-manager --region=${REGION} --format="value(status.url)")
    if curl -s "${VM_MANAGER_URL}/health" | grep -q "healthy"; then
        log "✓ VM Manager service is healthy"
    else
        log_warning "VM Manager service health check failed"
    fi
    
    # Test Database Pipeline health
    DATABASE_PIPELINE_URL=$(gcloud run services describe database-pipeline --region=${REGION} --format="value(status.url)")
    if curl -s "${DATABASE_PIPELINE_URL}/health" | grep -q "healthy"; then
        log "✓ Database Pipeline service is healthy"
    else
        log_warning "Database Pipeline service health check failed"
    fi
    
    # Test Orchestrator health
    ORCHESTRATOR_URL=$(gcloud run services describe pipeline-orchestrator --region=${REGION} --format="value(status.url)")
    if curl -s "${ORCHESTRATOR_URL}/health" | grep -q "healthy"; then
        log "✓ Orchestrator service is healthy"
    else
        log_warning "Orchestrator service health check failed"
    fi
}

# Main deployment function
main() {
    log "=== Starting Cloud Run Deployment ==="
    
    # Check command line arguments
    COMMAND=${1:-"full"}
    
    case $COMMAND in
        "check")
            check_prerequisites
            ;;
        "build")
            check_prerequisites
            configure_docker
            build_and_push_service "vm-manager"
            build_and_push_service "database-pipeline"
            build_and_push_service "orchestrator"
            ;;
        "deploy")
            deploy_infrastructure
            update_cloud_run_services
            ;;
        "test")
            test_services
            ;;
        "urls")
            get_service_urls
            ;;
        "full")
            check_prerequisites
            configure_docker
            build_and_push_service "vm-manager"
            build_and_push_service "database-pipeline"
            build_and_push_service "orchestrator"
            deploy_infrastructure
            update_cloud_run_services
            get_service_urls
            test_services
            ;;
        *)
            echo "Usage: $0 [check|build|deploy|test|urls|full]"
            echo "  check  - Check prerequisites only"
            echo "  build  - Build and push container images only"
            echo "  deploy - Deploy infrastructure and update services only"
            echo "  test   - Test service health only"
            echo "  urls   - Get service URLs only"
            echo "  full   - Complete deployment (default)"
            exit 1
            ;;
    esac
    
    log "=== Cloud Run Deployment Completed ==="
}

# Run main function
main "$@"
