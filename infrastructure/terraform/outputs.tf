output "vm_name" {
  description = "Name of the created VM instance"
  value       = google_compute_instance.pipeline_vm.name
}

output "vm_external_ip" {
  description = "External IP address of the VM"
  value       = google_compute_instance.pipeline_vm.network_interface[0].access_config[0].nat_ip
}

output "vm_internal_ip" {
  description = "Internal IP address of the VM"
  value       = google_compute_instance.pipeline_vm.network_interface[0].network_ip
}

output "vm_zone" {
  description = "Zone where the VM is deployed"
  value       = google_compute_instance.pipeline_vm.zone
}

output "service_account_email" {
  description = "Email of the service account attached to the VM"
  value       = "vm-cuba-buddy-data-ingestion@${var.project_id}.iam.gserviceaccount.com"
}

output "service_account_id" {
  description = "ID of the service account attached to the VM"
  value       = "vm-cuba-buddy-data-ingestion"
}

output "storage_bucket_name" {
  description = "Name of the GCS bucket for pipeline data"
  value       = google_storage_bucket.pipeline_data.name
}

output "storage_bucket_url" {
  description = "URL of the GCS bucket"
  value       = google_storage_bucket.pipeline_data.url
}

output "network_name" {
  description = "Name of the VPC network"
  value       = google_compute_network.pipeline_network.name
}

output "subnet_name" {
  description = "Name of the subnet"
  value       = google_compute_subnetwork.pipeline_subnet.name
}

output "ssh_command" {
  description = "SSH command to connect to the VM"
  value       = "gcloud compute ssh ${google_compute_instance.pipeline_vm.name} --zone=${google_compute_instance.pipeline_vm.zone} --project=${var.project_id}"
}

output "vm_logs_command" {
  description = "Command to view VM startup logs"
  value       = "gcloud compute instances get-serial-port-output ${google_compute_instance.pipeline_vm.name} --zone=${google_compute_instance.pipeline_vm.zone} --project=${var.project_id}"
}

# Cloud Run Service URLs
output "vm_manager_url" {
  description = "URL of the VM Manager Cloud Run service"
  value       = google_cloud_run_v2_service.vm_manager.uri
}

output "database_pipeline_url" {
  description = "URL of the Database Pipeline Cloud Run service"
  value       = google_cloud_run_v2_service.database_pipeline.uri
}

output "orchestrator_url" {
  description = "URL of the Pipeline Orchestrator Cloud Run service"
  value       = google_cloud_run_v2_service.orchestrator.uri
}

output "artifact_registry_repository" {
  description = "Artifact Registry repository for container images"
  value       = google_artifact_registry_repository.pipeline_repo.name
}
