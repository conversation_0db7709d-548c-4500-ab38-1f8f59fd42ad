# GCP Configuration
project_id   = "your-gcp-project-id"
project_name = "data-pipeline"
#region       = "us-central1"
#zone         = "us-central1-a"
reigion       = "europe-west10"
zone         = "europe-west10-c"


# VM Configuration
machine_type = "e2-standard-4"
vm_image     = "ubuntu-os-cloud/ubuntu-2204-lts"
disk_size    = 50

# AWS Configuration
aws_hostname = "your-aws-ec2-ip-address"
aws_user     = "your-aws-username"

# AWS Private Key (use heredoc syntax for multi-line strings)
# Copy your private key content between the EOT markers
aws_private_key = <<-EOT
-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_PRIVATE_KEY_CONTENT_HERE
REPLACE_WITH_ACTUAL_KEY_LINES
-----END OPENSSH PRIVATE KEY-----
EOT

# AWS Public Key
# Copy your public key content between the EOT markers
aws_public_key = <<-EOT
ssh-rsa YOUR_PUBLIC_KEY_CONTENT_HERE user@hostname
EOT

# GitHub Configuration
github_repo  = "https://github.com/your-username/your-repo-name"
github_token = "ghp_your_github_personal_access_token"

# Environment Configuration
environment      = "dev"
auto_delete_vm   = true
pipeline_schedule = "0 2 * * 0"  # Weekly on Sunday at 2 AM
