# Cloud Run Services Configuration

# Enable required APIs
resource "google_project_service" "cloud_run_api" {
  project = var.project_id
  service = "run.googleapis.com"
  
  disable_dependent_services = true
}

resource "google_project_service" "cloud_build_api" {
  project = var.project_id
  service = "cloudbuild.googleapis.com"
  
  disable_dependent_services = true
}

resource "google_project_service" "artifact_registry_api" {
  project = var.project_id
  service = "artifactregistry.googleapis.com"
  
  disable_dependent_services = true
}

# Artifact Registry for storing container images
resource "google_artifact_registry_repository" "pipeline_repo" {
  location      = var.region
  repository_id = "pipeline-services"
  description   = "Repository for data pipeline Cloud Run services"
  format        = "DOCKER"
  
  depends_on = [google_project_service.artifact_registry_api]
}

# Service Account for Cloud Run services
resource "google_service_account" "cloud_run_sa" {
  account_id   = "cloud-run-pipeline-sa"
  display_name = "Cloud Run Pipeline Service Account"
  description  = "Service account for Cloud Run pipeline services"
}

# IAM roles for Cloud Run service account
resource "google_project_iam_member" "cloud_run_compute_admin" {
  project = var.project_id
  role    = "roles/compute.admin"
  member  = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}

resource "google_project_iam_member" "cloud_run_logging_writer" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}

resource "google_project_iam_member" "cloud_run_monitoring_writer" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}

resource "google_project_iam_member" "cloud_run_storage_admin" {
  project = var.project_id
  role    = "roles/storage.admin"
  member  = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}

# VM Manager Cloud Run Service
resource "google_cloud_run_v2_service" "vm_manager" {
  name     = "vm-manager"
  location = var.region
  
  template {
    service_account = google_service_account.cloud_run_sa.email
    
    containers {
      image = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.pipeline_repo.repository_id}/vm-manager:latest"
      
      ports {
        container_port = 8080
      }
      
      env {
        name  = "GCP_PROJECT_ID"
        value = var.project_id
      }
      
      env {
        name  = "GCP_ZONE"
        value = var.zone
      }
      
      resources {
        limits = {
          cpu    = "2"
          memory = "2Gi"
        }
      }
    }
    
    scaling {
      min_instance_count = 0
      max_instance_count = 10
    }
    
    timeout = "3600s"  # 1 hour timeout
  }
  
  depends_on = [google_project_service.cloud_run_api]
}

# Database Pipeline Cloud Run Service
resource "google_cloud_run_v2_service" "database_pipeline" {
  name     = "database-pipeline"
  location = var.region
  
  template {
    service_account = google_service_account.cloud_run_sa.email
    
    containers {
      image = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.pipeline_repo.repository_id}/database-pipeline:latest"
      
      ports {
        container_port = 8080
      }
      
      env {
        name  = "VM_MANAGER_URL"
        value = google_cloud_run_v2_service.vm_manager.uri
      }
      
      env {
        name  = "AWS_HOSTNAME"
        value = var.aws_hostname
      }
      
      env {
        name  = "AWS_USER"
        value = var.aws_user
      }
      
      resources {
        limits = {
          cpu    = "1"
          memory = "1Gi"
        }
      }
    }
    
    scaling {
      min_instance_count = 0
      max_instance_count = 5
    }
    
    timeout = "3600s"  # 1 hour timeout
  }
  
  depends_on = [google_project_service.cloud_run_api]
}

# Orchestrator Cloud Run Service
resource "google_cloud_run_v2_service" "orchestrator" {
  name     = "pipeline-orchestrator"
  location = var.region
  
  template {
    service_account = google_service_account.cloud_run_sa.email
    
    containers {
      image = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.pipeline_repo.repository_id}/orchestrator:latest"
      
      ports {
        container_port = 8080
      }
      
      env {
        name  = "VM_MANAGER_URL"
        value = google_cloud_run_v2_service.vm_manager.uri
      }
      
      env {
        name  = "DATABASE_PIPELINE_URL"
        value = google_cloud_run_v2_service.database_pipeline.uri
      }
      
      env {
        name  = "GCP_PROJECT_ID"
        value = var.project_id
      }
      
      resources {
        limits = {
          cpu    = "1"
          memory = "1Gi"
        }
      }
    }
    
    scaling {
      min_instance_count = 0
      max_instance_count = 3
    }
    
    timeout = "3600s"  # 1 hour timeout
  }
  
  depends_on = [google_project_service.cloud_run_api]
}

# IAM policy to allow unauthenticated access (for GitHub Actions)
resource "google_cloud_run_service_iam_member" "vm_manager_invoker" {
  location = google_cloud_run_v2_service.vm_manager.location
  service  = google_cloud_run_v2_service.vm_manager.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

resource "google_cloud_run_service_iam_member" "database_pipeline_invoker" {
  location = google_cloud_run_v2_service.database_pipeline.location
  service  = google_cloud_run_v2_service.database_pipeline.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

resource "google_cloud_run_service_iam_member" "orchestrator_invoker" {
  location = google_cloud_run_v2_service.orchestrator.location
  service  = google_cloud_run_v2_service.orchestrator.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Allow Cloud Run services to communicate with each other
resource "google_cloud_run_service_iam_member" "vm_manager_internal" {
  location = google_cloud_run_v2_service.vm_manager.location
  service  = google_cloud_run_v2_service.vm_manager.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}

resource "google_cloud_run_service_iam_member" "database_pipeline_internal" {
  location = google_cloud_run_v2_service.database_pipeline.location
  service  = google_cloud_run_v2_service.database_pipeline.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.cloud_run_sa.email}"
}
