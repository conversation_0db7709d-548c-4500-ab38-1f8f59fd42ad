# Terraform Configuration

This directory contains the Terraform configuration for deploying the GCP data pipeline infrastructure.

## Setup Instructions

### 1. Create terraform.tfvars file

Copy the example file and fill in your actual values:

```bash
cp terraform.tfvars.example terraform.tfvars
```

### 2. Configure Variables

Edit `terraform.tfvars` with your actual values:

#### Required Variables:
- `project_id`: Your GCP project ID
- `aws_private_key`: Your AWS EC2 private key content (use heredoc format)
- `aws_public_key`: Your AWS EC2 public key content
- `aws_hostname`: Your AWS EC2 instance IP address
- `github_repo`: Your GitHub repository URL
- `github_token`: Your GitHub personal access token

#### Important: Multi-line String Format

For SSH keys, use Terraform's heredoc syntax:

```hcl
aws_private_key = <<-EOT
-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_ACTUAL_PRIVATE_KEY_CONTENT_HERE
EACH_LINE_OF_THE_KEY
-----<PERSON><PERSON> O<PERSON>ENSSH PRIVATE KEY-----
EOT
```

**DO NOT** use regular quoted strings for multi-line content like this:
```hcl
# ❌ WRONG - This will cause errors
aws_private_key = "-----BEGIN OPENSSH PRIVATE KEY-----
YOUR_KEY_CONTENT
-----END OPENSSH PRIVATE KEY-----"
```

### 3. Initialize and Deploy

```bash
# Initialize Terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply
```

## File Structure

- `main.tf`: Main Terraform configuration
- `variables.tf`: Variable definitions
- `outputs.tf`: Output definitions
- `startup-script.sh`: VM startup script
- `terraform.tfvars.example`: Example configuration file
- `terraform.tfvars`: Your actual configuration (gitignored)

## Security Notes

- The `terraform.tfvars` file is automatically ignored by Git
- Never commit sensitive data like private keys or tokens
- Use environment variables or secret management for CI/CD pipelines
- Consider using Terraform Cloud or similar for remote state management

## Troubleshooting

### Multi-line String Errors

If you see errors like "Invalid multi-line string" or "Unterminated template string":
1. Check that you're using heredoc syntax (`<<-EOT ... EOT`)
2. Ensure there are no extra quotes around heredoc blocks
3. Verify that each heredoc block has matching start and end markers

### Missing Variables

If you see "vars map does not contain key" errors:
1. Check that all required variables are defined in `terraform.tfvars`
2. Verify variable names match exactly between files
3. Ensure no typos in variable names
