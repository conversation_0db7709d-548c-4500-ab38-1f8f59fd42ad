#!/usr/bin/env python3
"""
Data Pipeline Runner
Extracts data from AWS EC2 MariaDB, transforms it, and uploads to GCS
"""

import os
import sys
import logging
import subprocess
import tempfile
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/pipeline_execution.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DataPipeline:
    def __init__(self):
        self.start_time = datetime.now()
        self.temp_dir = tempfile.mkdtemp()
        self.dump_file = os.path.join(self.temp_dir, 'aws_database_dump.sql')
        
        # Configuration
        self.aws_host = "trips"  # SSH alias configured in startup script
        self.local_db_user = "pipeline"
        self.local_db_password = "pipeline123"
        self.local_db_name = "pipeline_data"
        
        logger.info(f"Pipeline initialized at {self.start_time}")
        logger.info(f"Temporary directory: {self.temp_dir}")

    def test_ssh_connection(self):
        """Test SSH connection to AWS EC2"""
        logger.info("Testing SSH connection to AWS EC2...")
        
        try:
            result = subprocess.run(
                ['ssh', self.aws_host, 'echo "SSH connection test successful"'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info("SSH connection successful")
                logger.info(f"SSH response: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"SSH connection failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("SSH connection timeout")
            return False
        except Exception as e:
            logger.error(f"SSH connection error: {e}")
            return False

    def extract_data_from_aws(self):
        """Extract database dump from AWS EC2"""
        logger.info("Starting data extraction from AWS EC2...")
        
        try:
            # Create database dump on AWS EC2
            dump_command = [
                'ssh', self.aws_host,
                'mysqldump --single-transaction --routines --triggers --all-databases'
            ]
            
            logger.info("Creating database dump...")
            with open(self.dump_file, 'w') as f:
                result = subprocess.run(
                    dump_command,
                    stdout=f,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=1800  # 30 minutes timeout
                )
            
            if result.returncode == 0:
                dump_size = os.path.getsize(self.dump_file)
                logger.info(f"Database dump created successfully: {dump_size} bytes")
                return True
            else:
                logger.error(f"Database dump failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Database dump timeout (30 minutes)")
            return False
        except Exception as e:
            logger.error(f"Data extraction error: {e}")
            return False

    def load_data_to_local_db(self):
        """Load dumped data into local MariaDB"""
        logger.info("Loading data into local MariaDB...")
        
        try:
            # Import dump into local database
            import_command = [
                'mysql',
                f'-u{self.local_db_user}',
                f'-p{self.local_db_password}',
                self.local_db_name
            ]
            
            with open(self.dump_file, 'r') as f:
                result = subprocess.run(
                    import_command,
                    stdin=f,
                    capture_output=True,
                    text=True,
                    timeout=1800  # 30 minutes timeout
                )
            
            if result.returncode == 0:
                logger.info("Data loaded successfully into local MariaDB")
                
                # Verify data import
                verify_command = [
                    'mysql',
                    f'-u{self.local_db_user}',
                    f'-p{self.local_db_password}',
                    '-e', 'SHOW DATABASES;'
                ]
                
                verify_result = subprocess.run(
                    verify_command,
                    capture_output=True,
                    text=True
                )
                
                if verify_result.returncode == 0:
                    logger.info("Database verification successful")
                    logger.info(f"Available databases:\n{verify_result.stdout}")
                
                return True
            else:
                logger.error(f"Data import failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Data import timeout (30 minutes)")
            return False
        except Exception as e:
            logger.error(f"Data loading error: {e}")
            return False

    def transform_data(self):
        """Transform and process the data"""
        logger.info("Starting data transformation...")
        
        try:
            # Example transformation: export specific tables or processed data
            export_dir = os.path.join(self.temp_dir, 'processed_data')
            os.makedirs(export_dir, exist_ok=True)
            
            # Example: Export a summary of all tables
            tables_query = """
            SELECT 
                TABLE_SCHEMA as database_name,
                TABLE_NAME as table_name,
                TABLE_ROWS as row_count,
                DATA_LENGTH as data_size
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
            ORDER BY TABLE_SCHEMA, TABLE_NAME;
            """
            
            export_command = [
                'mysql',
                f'-u{self.local_db_user}',
                f'-p{self.local_db_password}',
                '-e', tables_query
            ]
            
            summary_file = os.path.join(export_dir, 'database_summary.txt')
            with open(summary_file, 'w') as f:
                result = subprocess.run(
                    export_command,
                    stdout=f,
                    stderr=subprocess.PIPE,
                    text=True
                )
            
            if result.returncode == 0:
                logger.info(f"Database summary exported to {summary_file}")
                
                # Create a processing report
                report_file = os.path.join(export_dir, 'processing_report.txt')
                with open(report_file, 'w') as f:
                    f.write(f"Data Pipeline Processing Report\n")
                    f.write(f"================================\n")
                    f.write(f"Start Time: {self.start_time}\n")
                    f.write(f"Processing Time: {datetime.now()}\n")
                    f.write(f"Dump File Size: {os.path.getsize(self.dump_file)} bytes\n")
                    f.write(f"Temp Directory: {self.temp_dir}\n")
                    f.write(f"Status: Data transformation completed\n")
                
                logger.info(f"Processing report created: {report_file}")
                return export_dir
            else:
                logger.error(f"Data transformation failed: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Data transformation error: {e}")
            return None

    def upload_to_gcs(self, data_dir):
        """Upload processed data to Google Cloud Storage"""
        logger.info("Uploading data to Google Cloud Storage...")
        
        try:
            # Get GCS bucket name from environment or use default pattern
            project_id = subprocess.run(
                ['gcloud', 'config', 'get-value', 'project'],
                capture_output=True,
                text=True
            ).stdout.strip()
            
            # Find the pipeline data bucket
            bucket_list = subprocess.run(
                ['gsutil', 'ls'],
                capture_output=True,
                text=True
            )
            
            pipeline_bucket = None
            for line in bucket_list.stdout.split('\n'):
                if 'pipeline-data' in line:
                    pipeline_bucket = line.strip().rstrip('/')
                    break
            
            if not pipeline_bucket:
                logger.error("Pipeline data bucket not found")
                return False
            
            logger.info(f"Using GCS bucket: {pipeline_bucket}")
            
            # Upload all files in the data directory
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            gcs_path = f"{pipeline_bucket}/pipeline_runs/{timestamp}/"
            
            upload_command = [
                'gsutil', '-m', 'cp', '-r',
                f"{data_dir}/*",
                gcs_path
            ]
            
            result = subprocess.run(
                upload_command,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"Data uploaded successfully to {gcs_path}")
                
                # Upload the original dump file as well
                dump_upload = subprocess.run([
                    'gsutil', 'cp', self.dump_file,
                    f"{gcs_path}database_dump.sql"
                ], capture_output=True, text=True)
                
                if dump_upload.returncode == 0:
                    logger.info("Database dump uploaded to GCS")
                
                return True
            else:
                logger.error(f"GCS upload failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"GCS upload error: {e}")
            return False

    def cleanup(self):
        """Clean up temporary files"""
        logger.info("Cleaning up temporary files...")
        
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info(f"Temporary directory cleaned up: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")

    def run(self):
        """Run the complete data pipeline"""
        logger.info("=== Starting Data Pipeline Execution ===")
        
        try:
            # Step 1: Test SSH connection
            if not self.test_ssh_connection():
                logger.error("SSH connection test failed, aborting pipeline")
                return False
            
            # Step 2: Extract data from AWS
            if not self.extract_data_from_aws():
                logger.error("Data extraction failed, aborting pipeline")
                return False
            
            # Step 3: Load data to local database
            if not self.load_data_to_local_db():
                logger.error("Data loading failed, aborting pipeline")
                return False
            
            # Step 4: Transform data
            processed_data_dir = self.transform_data()
            if not processed_data_dir:
                logger.error("Data transformation failed, aborting pipeline")
                return False
            
            # Step 5: Upload to GCS
            if not self.upload_to_gcs(processed_data_dir):
                logger.error("GCS upload failed, aborting pipeline")
                return False
            
            # Success
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            logger.info("=== Data Pipeline Completed Successfully ===")
            logger.info(f"Total execution time: {duration}")
            
            return True
            
        except Exception as e:
            logger.error(f"Pipeline execution error: {e}")
            return False
        
        finally:
            self.cleanup()

def main():
    """Main entry point"""
    pipeline = DataPipeline()
    
    try:
        success = pipeline.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
