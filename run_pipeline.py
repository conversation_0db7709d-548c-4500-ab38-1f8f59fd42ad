#!/usr/bin/env python3
"""
Enhanced Database Pipeline Runner
Performs MySQL database dump from GCP VM, imports to local database, and validates data
"""

import os
import sys
import logging
import subprocess
import tempfile
import mysql.connector
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/pipeline_execution.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabasePipeline:
    def __init__(self):
        self.start_time = datetime.now()
        self.temp_dir = tempfile.mkdtemp()

        # Configuration for the specific pipeline requirements
        self.aws_host = "trips"  # SSH alias configured in startup script
        self.local_db_user = "pipeline"
        self.local_db_password = "pipeline123"
        self.forge_db_name = "forge"  # Target database name as specified

        # File paths
        self.dump_file_path = os.path.expanduser("~/Documents/cuba_buddy/forge_dump.sql")
        self.temp_dump_file = os.path.join(self.temp_dir, 'forge_dump.sql')

        # Ensure the cuba_buddy directory exists
        os.makedirs(os.path.dirname(self.dump_file_path), exist_ok=True)

        logger.info(f"Database Pipeline initialized at {self.start_time}")
        logger.info(f"Temporary directory: {self.temp_dir}")
        logger.info(f"Target dump file: {self.dump_file_path}")

    def test_ssh_connection(self):
        """Test SSH connection to AWS EC2 using the trips alias"""
        logger.info("Testing SSH connection to AWS EC2 using 'trips' alias...")

        try:
            # Load SSH agent environment if available
            self._load_ssh_agent()

            # Test basic SSH connection
            result = subprocess.run(
                ['ssh', '-o', 'ConnectTimeout=30', self.aws_host, 'echo "SSH connection test successful"'],
                capture_output=True,
                text=True,
                timeout=45
            )

            if result.returncode == 0:
                logger.info("SSH connection successful")
                logger.info(f"SSH response: {result.stdout.strip()}")

                # Test MySQL availability on remote host
                mysql_test = subprocess.run(
                    ['ssh', self.aws_host, 'mysql --version'],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if mysql_test.returncode == 0:
                    logger.info(f"MySQL available on remote host: {mysql_test.stdout.strip()}")
                    return True
                else:
                    logger.warning("MySQL not available on remote host, but SSH connection works")
                    return True
            else:
                logger.error(f"SSH connection failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("SSH connection timeout")
            return False
        except Exception as e:
            logger.error(f"SSH connection error: {e}")
            return False

    def _load_ssh_agent(self):
        """Load SSH agent environment if available"""
        try:
            ssh_agent_env = os.path.expanduser("~/.ssh/ssh_agent_env")
            if os.path.exists(ssh_agent_env):
                with open(ssh_agent_env, 'r') as f:
                    for line in f:
                        if line.startswith('export '):
                            key, value = line.replace('export ', '').strip().split('=', 1)
                            os.environ[key] = value
                logger.info("SSH agent environment loaded")
        except Exception as e:
            logger.warning(f"Could not load SSH agent environment: {e}")

    def dump_forge_database(self):
        """
        Execute SSH command to dump MySQL database from GCP VM
        Command: ssh -TC trips 'mysqldump forge --ignore-table=forge.activity_log' > ~/Documents/cuba_buddy/forge_dump.sql
        """
        logger.info("Starting MySQL database dump from GCP VM...")

        try:
            # Load SSH agent environment
            self._load_ssh_agent()

            # Construct the mysqldump command as specified
            dump_command = [
                'ssh', '-TC', self.aws_host,
                'mysqldump forge --ignore-table=forge.activity_log'
            ]

            logger.info(f"Executing dump command: {' '.join(dump_command)}")
            logger.info(f"Output will be saved to: {self.dump_file_path}")

            # Execute the dump command and save to the specified location
            with open(self.dump_file_path, 'w') as f:
                result = subprocess.run(
                    dump_command,
                    stdout=f,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=1800  # 30 minutes timeout
                )

            if result.returncode == 0:
                dump_size = os.path.getsize(self.dump_file_path)
                logger.info(f"Database dump completed successfully!")
                logger.info(f"Dump file size: {dump_size} bytes")
                logger.info(f"Dump file location: {self.dump_file_path}")

                # Also create a copy in temp directory for processing
                import shutil
                shutil.copy2(self.dump_file_path, self.temp_dump_file)
                logger.info(f"Temporary copy created at: {self.temp_dump_file}")

                return True
            else:
                logger.error(f"Database dump failed!")
                logger.error(f"Error output: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("Database dump timeout (30 minutes)")
            return False
        except Exception as e:
            logger.error(f"Database dump error: {e}")
            return False

    def import_forge_database(self):
        """
        Import the forge_dump.sql file into a local MySQL database named 'forge'
        Ensures all data from the dump is loaded into the MySQL database
        """
        logger.info("Importing forge database dump into local MySQL...")

        try:
            # First, create the forge database if it doesn't exist
            create_db_command = [
                'mysql',
                f'-u{self.local_db_user}',
                f'-p{self.local_db_password}',
                '-e', f'CREATE DATABASE IF NOT EXISTS {self.forge_db_name};'
            ]

            logger.info(f"Creating database '{self.forge_db_name}' if it doesn't exist...")
            create_result = subprocess.run(
                create_db_command,
                capture_output=True,
                text=True,
                timeout=60
            )

            if create_result.returncode != 0:
                logger.error(f"Failed to create database: {create_result.stderr}")
                return False

            logger.info(f"Database '{self.forge_db_name}' is ready")

            # Import the dump file into the forge database
            import_command = [
                'mysql',
                f'-u{self.local_db_user}',
                f'-p{self.local_db_password}',
                self.forge_db_name
            ]

            logger.info(f"Importing dump file: {self.temp_dump_file}")
            with open(self.temp_dump_file, 'r') as f:
                result = subprocess.run(
                    import_command,
                    stdin=f,
                    capture_output=True,
                    text=True,
                    timeout=1800  # 30 minutes timeout
                )

            if result.returncode == 0:
                logger.info(f"Data imported successfully into '{self.forge_db_name}' database")

                # Verify the import by checking tables
                verify_command = [
                    'mysql',
                    f'-u{self.local_db_user}',
                    f'-p{self.local_db_password}',
                    self.forge_db_name,
                    '-e', 'SHOW TABLES;'
                ]

                verify_result = subprocess.run(
                    verify_command,
                    capture_output=True,
                    text=True
                )

                if verify_result.returncode == 0:
                    logger.info("Database import verification successful")
                    logger.info(f"Tables in '{self.forge_db_name}' database:\n{verify_result.stdout}")

                    # Check if trips table exists
                    if 'trips' in verify_result.stdout:
                        logger.info("✓ 'trips' table found in the database")
                    else:
                        logger.warning("⚠ 'trips' table not found in the database")

                return True
            else:
                logger.error(f"Data import failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("Data import timeout (30 minutes)")
            return False
        except Exception as e:
            logger.error(f"Database import error: {e}")
            return False

    def validate_database_query(self):
        """
        Create a Python script that:
        - Establishes a connection to the local MySQL database 'forge'
        - Executes the SQL query: SELECT COUNT(*) FROM trips WHERE is_booker_version=1
        - Prints the result in the format: "DB Query output resulting in these rows: [count]"
        """
        logger.info("Executing database validation query...")

        try:
            # Method 1: Using mysql command line (more reliable in this environment)
            query = "SELECT COUNT(*) FROM trips WHERE is_booker_version=1;"

            query_command = [
                'mysql',
                f'-u{self.local_db_user}',
                f'-p{self.local_db_password}',
                self.forge_db_name,
                '-e', query
            ]

            logger.info(f"Executing query: {query}")
            result = subprocess.run(
                query_command,
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode == 0:
                # Parse the output to get the count
                output_lines = result.stdout.strip().split('\n')
                if len(output_lines) >= 2:
                    count = output_lines[1].strip()  # Second line contains the count
                    logger.info(f"DB Query output resulting in these rows: {count}")
                    print(f"DB Query output resulting in these rows: {count}")
                    return True, count
                else:
                    logger.error("Unexpected query output format")
                    logger.error(f"Raw output: {result.stdout}")
                    return False, None
            else:
                logger.error(f"Database query failed: {result.stderr}")
                return False, None

        except subprocess.TimeoutExpired:
            logger.error("Database query timeout")
            return False, None
        except Exception as e:
            logger.error(f"Database query error: {e}")
            return False, None

    def validate_database_with_python_connector(self):
        """
        Alternative method using Python MySQL connector for database validation
        """
        logger.info("Attempting database validation using Python MySQL connector...")

        try:
            import mysql.connector

            # Establish connection to the forge database
            connection = mysql.connector.connect(
                host='localhost',
                user=self.local_db_user,
                password=self.local_db_password,
                database=self.forge_db_name
            )

            cursor = connection.cursor()

            # Execute the validation query
            query = "SELECT COUNT(*) FROM trips WHERE is_booker_version=1"
            cursor.execute(query)

            result = cursor.fetchone()
            count = result[0] if result else 0

            logger.info(f"DB Query output resulting in these rows: {count}")
            print(f"DB Query output resulting in these rows: {count}")

            cursor.close()
            connection.close()

            return True, count

        except ImportError:
            logger.warning("mysql.connector not available, falling back to command line method")
            return self.validate_database_query()
        except Exception as e:
            logger.error(f"Python connector database query error: {e}")
            return False, None

    def run(self):
        """
        Execute the complete database pipeline:
        1. Test SSH connection
        2. Dump MySQL database from GCP VM
        3. Import to local MySQL database
        4. Execute validation query
        """
        logger.info("=== Starting Enhanced Database Pipeline ===")
        logger.info("Pipeline Steps:")
        logger.info("1. Test SSH connection to GCP VM")
        logger.info("2. Dump 'forge' database (excluding activity_log table)")
        logger.info("3. Import dump to local MySQL 'forge' database")
        logger.info("4. Execute validation query on trips table")

        try:
            # Step 1: Test SSH connection
            logger.info("\n--- Step 1: Testing SSH Connection ---")
            if not self.test_ssh_connection():
                logger.error("SSH connection test failed, aborting pipeline")
                return False

            # Step 2: Dump forge database from GCP VM
            logger.info("\n--- Step 2: Dumping Database from GCP VM ---")
            if not self.dump_forge_database():
                logger.error("Database dump failed, aborting pipeline")
                return False

            # Step 3: Import database to local MySQL
            logger.info("\n--- Step 3: Importing Database to Local MySQL ---")
            if not self.import_forge_database():
                logger.error("Database import failed, aborting pipeline")
                return False

            # Step 4: Execute validation query
            logger.info("\n--- Step 4: Executing Validation Query ---")
            success, count = self.validate_database_query()
            if not success:
                logger.warning("Primary validation method failed, trying alternative...")
                success, count = self.validate_database_with_python_connector()

            if not success:
                logger.error("Database validation failed, but pipeline completed other steps")
                return False

            # Success
            end_time = datetime.now()
            duration = end_time - self.start_time

            logger.info("\n=== Database Pipeline Completed Successfully ===")
            logger.info(f"Total execution time: {duration}")
            logger.info(f"Final validation result: {count} rows found")

            return True

        except Exception as e:
            logger.error(f"Pipeline execution error: {e}")
            return False

        finally:
            self.cleanup()

    def cleanup(self):
        """Clean up temporary files and directories"""
        logger.info("Cleaning up temporary files...")

        try:
            if os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir)
                logger.info(f"Temporary directory removed: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Could not clean up temporary directory: {e}")



def main():
    """Main entry point"""
    pipeline = DatabasePipeline()
    
    try:
        success = pipeline.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
