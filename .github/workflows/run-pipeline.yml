name: Run Data Pipeline

on:
  schedule:
    - cron: '0 2 * * 0'  # Weekly on Sunday at 2 AM UTC
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run pipeline in'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      skip_vm_creation:
        description: 'Skip VM creation (use existing VM)'
        required: false
        default: false
        type: boolean
      pipeline_action:
        description: 'Pipeline action to perform'
        required: true
        default: 'run-with-cleanup'
        type: choice
        options:
          - run-with-cleanup
          - run-keep-vm
          - test-ssh-only
          - destroy-existing

env:
  TF_VERSION: '1.6.0'
  TF_WORKING_DIR: './infrastructure/terraform'

jobs:
  debug-inputs:
    name: Debug Workflow Inputs
    runs-on: ubuntu-latest
    steps:
      - name: Display Workflow Inputs
        run: |
          echo "=== Workflow Debug Information ==="
          echo "Triggered by: ${{ github.event_name }}"
          echo "Branch: ${{ github.ref }}"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Skip VM Creation: ${{ github.event.inputs.skip_vm_creation }}"
          echo "Pipeline Action: ${{ github.event.inputs.pipeline_action || 'run-with-cleanup' }}"
          echo ""
          echo "=== Job Execution Conditions ==="
          echo "Is workflow_dispatch: ${{ github.event_name == 'workflow_dispatch' }}"
          echo "Is scheduled: ${{ github.event_name == 'schedule' }}"
          echo "Should run destroy: ${{ github.event.inputs.pipeline_action == 'destroy-existing' }}"
          echo "Should create VM: ${{ !github.event.inputs.skip_vm_creation && github.event.inputs.pipeline_action != 'destroy-existing' }}"
          echo ""
          if [ "${{ github.event_name }}" != "workflow_dispatch" ] && [ "${{ github.event_name }}" != "schedule" ]; then
            echo "⚠️  WARNING: This workflow should only be triggered manually or by schedule!"
            echo "⚠️  Use 'Deploy Data Pipeline Infrastructure' for push-triggered deployments."
            echo "⚠️  No jobs will run because this was triggered by: ${{ github.event_name }}"
          fi
          echo "=== End Debug ==="

  check-job-conditions:
    name: Check Job Execution Conditions
    runs-on: ubuntu-latest
    steps:
      - name: Display Job Conditions
        run: |
          echo "=== Job Execution Conditions ==="
          echo "Event: ${{ github.event_name }}"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Skip VM Creation: ${{ github.event.inputs.skip_vm_creation }}"
          echo "Pipeline Action: ${{ github.event.inputs.pipeline_action || 'run-with-cleanup' }}"
          echo ""
          echo "=== Job Should Run Checks ==="
          echo "Is manual/scheduled: ${{ github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' }}"
          echo "Should destroy: ${{ github.event.inputs.pipeline_action == 'destroy-existing' }}"
          echo "Should create VM: ${{ github.event.inputs.skip_vm_creation != 'true' && github.event.inputs.pipeline_action != 'destroy-existing' }}"
          echo "Should run pipeline: ${{ github.event.inputs.pipeline_action != 'destroy-existing' }}"
          echo ""
          echo "=== Expected Job Execution ==="
          if [ "${{ github.event.inputs.pipeline_action }}" == "destroy-existing" ]; then
            echo "✅ destroy-existing job should run"
            echo "❌ create-vm job should NOT run"
            echo "❌ run-pipeline job should NOT run"
            echo "❌ cleanup-vm job should NOT run"
          elif [ "${{ github.event.inputs.skip_vm_creation }}" == "true" ]; then
            echo "❌ destroy-existing job should NOT run"
            echo "❌ create-vm job should NOT run (using existing VM)"
            echo "✅ run-pipeline job should run"
            echo "❌ cleanup-vm job should NOT run (VM not created)"
          else
            echo "❌ destroy-existing job should NOT run"
            echo "✅ create-vm job should run"
            echo "✅ run-pipeline job should run"
            echo "✅ cleanup-vm job should run (if cleanup action)"
          fi

  destroy-existing:
    name: Destroy Existing Resources
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.pipeline_action == 'destroy-existing'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Destroy Infrastructure
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          # Create terraform.tfvars for destroy
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          EOF

          # Update backend configuration
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

          # Initialize and destroy
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"

          echo "=== Destroying all resources ==="
          terraform destroy -auto-approve

          echo "=== Cleanup completed ==="
  create-vm:
    name: Create Pipeline VM
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      github.event.inputs.skip_vm_creation != 'true' &&
      github.event.inputs.pipeline_action != 'destroy-existing'
    
    outputs:
      vm_name: ${{ steps.get-vm-info.outputs.vm_name }}
      vm_zone: ${{ steps.get-vm-info.outputs.vm_zone }}
      
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Verify SSH Keys Configuration
        run: |
          echo "=== SSH Keys Configuration Debug ==="
          echo "AWS_PRIVATE_KEY length: ${#AWS_PRIVATE_KEY}"
          echo "AWS_PUBLIC_KEY length: ${#AWS_PUBLIC_KEY}"
          echo "AWS_HOSTNAME: $AWS_HOSTNAME"
          echo "AWS_USER: $AWS_USER"

          # Check if keys start with expected headers
          if [[ "$AWS_PRIVATE_KEY" == -----BEGIN* ]]; then
            echo "✓ Private key has correct header"
          else
            echo "✗ Private key missing header"
          fi

          if [[ "$AWS_PUBLIC_KEY" == ssh-* ]]; then
            echo "✓ Public key has correct format"
          else
            echo "✗ Public key missing ssh- prefix"
          fi
        env:
          AWS_PRIVATE_KEY: ${{ secrets.AWS_PRIVATE_KEY }}
          AWS_PUBLIC_KEY: ${{ secrets.AWS_PUBLIC_KEY }}
          AWS_HOSTNAME: ${{ secrets.AWS_HOSTNAME }}
          AWS_USER: ${{ secrets.AWS_USER }}

      - name: Create terraform.tfvars
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          auto_delete_vm = true
          EOF

          echo "=== Terraform Variables Debug ==="
          echo "terraform.tfvars file created successfully"
          echo "File size: $(wc -c < terraform.tfvars) bytes"
          echo "Lines in file: $(wc -l < terraform.tfvars)"

          # Show non-sensitive parts of the file
          echo "Non-sensitive configuration:"
          grep -E "^(project_id|project_name|region|zone|machine_type|aws_hostname|aws_user|environment|auto_delete_vm)" terraform.tfvars || echo "Could not extract non-sensitive config"

      - name: Update Terraform Backend Configuration
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

      - name: Terraform Init
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"

      - name: Terraform Apply
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: terraform apply -auto-approve

      - name: Get VM Information
        id: get-vm-info
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          echo "=== Terraform Output Debug ==="
          echo "Current directory: $(pwd)"
          echo "Terraform state file exists: $(test -f terraform.tfstate && echo 'yes' || echo 'no')"

          # List all available outputs
          echo "Available Terraform outputs:"
          terraform output || echo "No outputs available"

          # Get outputs with error handling
          VM_NAME=$(terraform output -raw vm_name 2>/dev/null || echo "")
          VM_ZONE=$(terraform output -raw vm_zone 2>/dev/null || echo "")

          echo "Raw VM_NAME output: '$VM_NAME'"
          echo "Raw VM_ZONE output: '$VM_ZONE'"

          # Fallback: try to get zone from terraform show if output is empty
          if [ -z "$VM_ZONE" ]; then
            echo "VM_ZONE is empty, trying to extract from terraform show..."
            VM_ZONE=$(terraform show -json | jq -r '.values.root_module.resources[] | select(.type=="google_compute_instance" and .name=="pipeline_vm") | .values.zone' 2>/dev/null || echo "")
            echo "Zone from terraform show: '$VM_ZONE'"
          fi

          # Final fallback: use the default zone from variables
          if [ -z "$VM_ZONE" ]; then
            echo "Still no zone found, using default zone..."
            #VM_ZONE="us-central1-a"
            VM_ZONE="europe-west10-c"
            echo "Using default zone: '$VM_ZONE'"
          fi

          echo "vm_name=$VM_NAME" >> $GITHUB_OUTPUT
          echo "vm_zone=$VM_ZONE" >> $GITHUB_OUTPUT

          echo "Final VM Name: $VM_NAME"
          echo "Final VM Zone: $VM_ZONE"

      - name: Wait for VM Startup
        run: |
          VM_NAME="${{ steps.get-vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.get-vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "Waiting for VM startup to complete..."
          
          # Wait up to 15 minutes for startup script to complete
          for i in {1..90}; do
            echo "Checking startup progress... (attempt $i/90)"

            # Check if startup script completed successfully (multiple patterns for robustness)
            SERIAL_OUTPUT=$(gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID 2>/dev/null || echo "")
            if echo "$SERIAL_OUTPUT" | grep -q "Data Pipeline VM Startup Script Completed Successfully" || \
               echo "$SERIAL_OUTPUT" | grep -q "STARTUP_COMPLETE:" || \
               echo "$SERIAL_OUTPUT" | grep -q "=== Data Pipeline VM Startup Script Completed Successfully" || \
               (echo "$SERIAL_OUTPUT" | grep -q "Finished Google Compute Engine Startup Scripts" && \
                echo "$SERIAL_OUTPUT" | grep -q "Finished running startup scripts"); then
              echo "VM startup completed successfully!"
              break
            fi

            # Show partial logs every 5 attempts to help debug
            if [ $((i % 5)) -eq 0 ]; then
              echo "=== Partial startup logs (attempt $i) ==="
              gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID 2>/dev/null | tail -10 || echo "Could not get logs"
            fi

            if [ $i -eq 90 ]; then
              echo "VM startup timeout after 15 minutes - showing full logs"
              gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID 2>/dev/null || echo "Could not get final logs"
              exit 1
            fi

            sleep 10
          done

      - name: Display VM Startup Logs
        run: |
          VM_NAME="${{ steps.get-vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.get-vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"
          
          echo "=== VM Startup Logs ==="
          gcloud compute instances get-serial-port-output $VM_NAME --zone=$VM_ZONE --project=$PROJECT_ID || echo "Could not retrieve startup logs"

  run-pipeline:
    name: Execute Data Pipeline
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    needs: [create-vm]
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      always() &&
      (needs.create-vm.result == 'success' || github.event.inputs.skip_vm_creation == 'true') &&
      github.event.inputs.pipeline_action != 'destroy-existing'
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Get VM Information
        id: vm-info
        run: |
          echo "=== VM Information Debug ==="
          echo "Skip VM Creation: ${{ github.event.inputs.skip_vm_creation }}"
          echo "Create VM Job Result: ${{ needs.create-vm.result }}"
          echo "Create VM Outputs Available: ${{ needs.create-vm.outputs.vm_name != '' }}"

          if [ "${{ github.event.inputs.skip_vm_creation }}" == "true" ]; then
            # Use existing VM
            VM_NAME="data-pipeline-${{ github.event.inputs.environment || 'dev' }}-pipeline-vm"
            VM_ZONE="${{ secrets.GCP_ZONE }}"
            echo "Using existing VM: $VM_NAME in zone $VM_ZONE"
          else
            # Use newly created VM
            VM_NAME="${{ needs.create-vm.outputs.vm_name }}"
            VM_ZONE="${{ needs.create-vm.outputs.vm_zone }}"
            echo "Using newly created VM: $VM_NAME in zone $VM_ZONE"

            # Validate and fix VM information
            if [ -z "$VM_NAME" ]; then
              echo "ERROR: VM_NAME is missing!"
              echo "Create VM job result: ${{ needs.create-vm.result }}"
              exit 1
            fi

            if [ -z "$VM_ZONE" ]; then
              echo "WARNING: VM_ZONE is missing, trying fallback methods..."

              # Try to get zone from GCP directly
              VM_ZONE=$(gcloud compute instances list --filter="name=$VM_NAME" --format="value(zone)" 2>/dev/null | head -1)

              if [ -n "$VM_ZONE" ]; then
                echo "Found VM zone from gcloud: $VM_ZONE"
              else
                echo "Could not find VM zone, using default zone"
                #VM_ZONE="us-central1-a"
                VM_ZONE="europe-west10-c"
              fi
            fi

            echo "Final VM information: $VM_NAME in zone $VM_ZONE"
          fi

          echo "vm_name=$VM_NAME" >> $GITHUB_OUTPUT
          echo "vm_zone=$VM_ZONE" >> $GITHUB_OUTPUT
          echo "Final VM: $VM_NAME in $VM_ZONE"

      - name: Execute Pipeline on VM
        run: |
          VM_NAME="${{ steps.vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"

          echo "Executing data pipeline on VM: $VM_NAME"

          # Create a script file to avoid complex quoting issues
          cat > /tmp/pipeline_script.sh << 'EOF'
          #!/bin/bash
          set -e
          echo 'Starting data pipeline execution...'

          # Debug information
          echo "=== Debug Information ==="
          echo "Current user: $(whoami)"
          echo "Current directory: $(pwd)"
          echo "Home directory: $HOME"
          echo "SSH directory contents:"
          ls -la /home/<USER>/.ssh/ 2>/dev/null || echo "SSH directory not accessible"

          # Test SSH connection as pipeline user
          sudo -u pipeline bash -c '
            cd /home/<USER>

            echo "Testing SSH connection to AWS EC2..."

            # Load SSH agent environment and ensure key is loaded
            echo "Loading SSH agent environment..."
            if [ -f ~/.ssh/load_ssh_agent.sh ]; then
                source ~/.ssh/load_ssh_agent.sh
            else
                echo "SSH agent loader not found, setting up manually..."
                eval "$(ssh-agent -s)"
                ssh-add ~/.ssh/aws_private_key
            fi

            # List loaded keys for debugging
            echo "Keys currently loaded in SSH agent:"
            ssh-add -l

            # Test SSH connection
            if ssh trips "echo \"SSH connection verified with no verbose : $(date)\""; then
              echo "SSH connection successful without verbose method!"

              # Create the target directory if it does not exist
              echo "Creating target directory..."
              mkdir -p /home/<USER>/cuba-buddy/De
              echo "Directory created: /home/<USER>/cuba-buddy/De"

              # Get dump with error handling
              echo "Starting database dump..."
              if ssh trips "mysqldump --no-data forge --ignore-table=forge.activity_log" > /home/<USER>/cuba-buddy/De/output_file.sql; then
                echo "Database dump successful! File size: $(wc -l < /home/<USER>/cuba-buddy/De/output_file.sql) lines"
                echo "First 100 lines:"
                head -n 100 /home/<USER>/cuba-buddy/De/output_file.sql
              else
                echo "Database dump failed! Trying with explicit SSH key..."
                if ssh -i ~/.ssh/aws_private_key trips "mysqldump --no-data forge --ignore-table=forge.activity_log" > /home/<USER>/cuba-buddy/De/output_file.sql; then
                  echo "Database dump successful with explicit key! File size: $(wc -l < /home/<USER>/cuba-buddy/De/output_file.sql) lines"
                  echo "First 100 lines:"
                  head -n 100 /home/<USER>/cuba-buddy/De/output_file.sql
                else
                  echo "Database dump failed even with explicit key!"
                  exit 1
                fi
              fi
            elif ssh -i ~/.ssh/aws_private_key trips "echo \"SSH connection verified with the verbose method: $(date)\""; then
              echo "SSH connection successful with verbose method!"

              # Create the target directory if it does not exist
              echo "Creating target directory..."
              mkdir -p /home/<USER>/cuba-buddy/De
              echo "Directory created: /home/<USER>/cuba-buddy/De"

              # Get dump with error handling
              echo "Starting database dump with explicit SSH key..."
              if ssh -i ~/.ssh/aws_private_key trips "mysqldump --no-data forge --ignore-table=forge.activity_log" > /home/<USER>/cuba-buddy/De/output_file.sql; then
                echo "Database dump successful! File size: $(wc -l < /home/<USER>/cuba-buddy/De/output_file.sql) lines"
                echo "First 100 lines:"
                head -n 100 /home/<USER>/cuba-buddy/De/output_file.sql
              else
                echo "Database dump failed!"
                exit 1
              fi
            else
              echo "SSH connection failed, attempting troubleshooting..."
              echo "SSH configuration:"
              cat ~/.ssh/config
              echo "Private key permissions:"
              ls -la ~/.ssh/aws_private_key
              echo "Private key fingerprint:"
              ssh-keygen -lf ~/.ssh/aws_private_key
              #ssh -i ~/.ssh/aws_private_key trips
              echo "Public key content:"
              cat ~/.ssh/aws_public_key
              echo "Testing direct SSH connection with verbose output..."
              ssh -v -o ConnectTimeout=10 -o BatchMode=yes trips "echo \"Direct SSH test: $(date)\"" 2>&1 || echo "Direct SSH also failed"
            fi

            # If pipeline repository exists, run the pipeline
            if [ -d "/home/<USER>/pipeline-repo" ]; then
              cd /home/<USER>/pipeline-repo

              # Activate virtual environment if it exists
              if [ -f "venv/bin/activate" ]; then
                source venv/bin/activate
              fi

              # Run pipeline script if it exists
              if [ -f "run_pipeline.py" ]; then
                echo "Running Python pipeline..."
                python run_pipeline.py
              elif [ -f "run_pipeline.sh" ]; then
                echo "Running shell pipeline..."
                bash run_pipeline.sh
              else
                echo "No pipeline script found, running basic data extraction test..."

                # Basic test: dump a sample table from AWS EC2
                ssh trips "mysqldump --single-transaction --routines --triggers --all-databases" > /tmp/aws_db_dump.sql

                # Import to local MariaDB
                mysql -u pipeline -ppipeline123 pipeline_data < /tmp/aws_db_dump.sql

                # Basic verification
                mysql -u pipeline -ppipeline123 -e "SHOW DATABASES;"

                echo "Basic pipeline test completed successfully"
              fi
            else
              echo "Pipeline repository not found, running basic SSH test only"
            fi

            echo "Pipeline execution completed at: $(date)"
          '
          EOF

          # Validate VM information before executing
          if [ -z "$VM_NAME" ] || [ -z "$VM_ZONE" ]; then
            echo "ERROR: Cannot execute pipeline - VM information is missing!"
            echo "VM_NAME: '$VM_NAME'"
            echo "VM_ZONE: '$VM_ZONE'"
            echo "This usually means the create-vm job failed or was skipped incorrectly."
            exit 1
          fi

          echo "Transferring script to VM: $VM_NAME"
          # Execute the script on the VM
          gcloud compute scp /tmp/pipeline_script.sh $VM_NAME:/tmp/pipeline_script.sh \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID

          echo "Executing pipeline script on VM"
          gcloud compute ssh $VM_NAME \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID \
            --command="chmod +x /tmp/pipeline_script.sh && /tmp/pipeline_script.sh" \
            || echo "Pipeline execution failed"

      - name: Collect Pipeline Logs
        run: |
          VM_NAME="${{ steps.vm-info.outputs.vm_name }}"
          VM_ZONE="${{ steps.vm-info.outputs.vm_zone }}"
          PROJECT_ID="${{ secrets.GCP_PROJECT_ID }}"

          echo "=== Collecting Pipeline Execution Logs ==="

          # Get the latest logs from the VM
          gcloud compute ssh $VM_NAME \
            --zone=$VM_ZONE \
            --project=$PROJECT_ID \
            --command="
              echo '=== Pipeline Startup Log ==='
              sudo cat /var/log/pipeline-startup.log 2>/dev/null || echo 'Startup log not found'

              echo '=== SSH Test Results ==='
              sudo -u pipeline cat /home/<USER>/ssh_test_output.log 2>/dev/null || echo 'SSH test log not found'

              echo '=== SSH Configuration Debug ==='
              echo 'SSH directory contents:'
              sudo -u pipeline ls -la /home/<USER>/.ssh/ 2>/dev/null || echo 'SSH directory not accessible'
              echo 'SSH config file:'
              sudo -u pipeline cat /home/<USER>/.ssh/config 2>/dev/null || echo 'SSH config not found'
              echo 'Private key file info:'
              sudo -u pipeline ls -la /home/<USER>/.ssh/aws_private_key 2>/dev/null || echo 'Private key not found'
              echo 'Public key file info:'
              sudo -u pipeline ls -la /home/<USER>/.ssh/aws_public_key 2>/dev/null || echo 'Public key not found'

              echo '=== VM Setup Summary ==='
              sudo -u pipeline cat /home/<USER>/vm_setup_summary.txt 2>/dev/null || echo 'Setup summary not found'
            " || echo "Could not collect logs from VM"

  cleanup-vm:
    name: Cleanup Pipeline VM
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    needs: [create-vm, run-pipeline]
    if: |
      (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') &&
      always() &&
      needs.create-vm.result == 'success' &&
      github.event.inputs.skip_vm_creation != 'true' &&
      (github.event.inputs.pipeline_action == 'run-with-cleanup' ||
       github.event.inputs.pipeline_action == 'test-ssh-only' ||
       !github.event.inputs.pipeline_action)
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
          terraform_wrapper: false

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Destroy Infrastructure
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          # Create terraform.tfvars for destroy
          cat > terraform.tfvars << EOF
          project_id = "${{ secrets.GCP_PROJECT_ID }}"
          project_name = "data-pipeline"
          region = "${{ secrets.GCP_REGION }}"
          zone = "${{ secrets.GCP_ZONE }}"
          machine_type = "${{ secrets.VM_MACHINE_TYPE || 'e2-standard-4' }}"
          aws_private_key = "${{ secrets.AWS_PRIVATE_KEY }}"
          aws_public_key = "${{ secrets.AWS_PUBLIC_KEY }}"
          aws_hostname = "${{ secrets.AWS_HOSTNAME }}"
          aws_user = "${{ secrets.AWS_USER }}"
          github_repo = "${{ github.server_url }}/${{ github.repository }}"
          github_token = "${{ secrets.GITHUB_TOKEN }}"
          environment = "${{ github.event.inputs.environment || 'dev' }}"
          EOF
          
          # Update backend configuration
          BUCKET_NAME="${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}"
          sed -i "s/your-terraform-state-bucket/$BUCKET_NAME/g" main.tf

          # Initialize and destroy
          terraform init \
            -backend-config="bucket=${{ secrets.GCP_PROJECT_ID }}-terraform-state-${{ github.event.inputs.environment || 'dev' }}" \
            -backend-config="prefix=terraform/state/${{ github.event.inputs.environment || 'dev' }}"
          
          terraform destroy -auto-approve
