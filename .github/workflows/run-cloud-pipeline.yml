name: Run Cloud Data Pipeline

on:
  schedule:
    - cron: '0 2 * * 0'  # Weekly on Sunday at 2 AM UTC
  push:
    branches:
      - main
      - production
      - testing
    paths:
      - 'cloud-functions/**'
      - 'infrastructure/terraform/cloud-run.tf'
      - '.github/workflows/run-cloud-pipeline.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run pipeline in'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      pipeline_action:
        description: 'Pipeline action to perform'
        required: true
        default: 'run-complete-workflow'
        type: choice
        options:
          - run-complete-workflow
          - deploy-cloud-run-only
          - test-services-only
          - run-pipeline-only
      deploy_cloud_run:
        description: 'Deploy/Update Cloud Run services'
        required: false
        default: true
        type: boolean

env:
  TF_VERSION: '1.6.0'
  TF_WORKING_DIR: './infrastructure/terraform'
  PROJECT_ID: 'external-data-source-437915'
  REGION: 'europe-west3'
  ZONE: 'europe-west3-a'

jobs:
  debug-inputs:
    name: Debug Workflow Inputs
    runs-on: ubuntu-latest
    steps:
      - name: Display Workflow Inputs
        run: |
          echo "=== Cloud Pipeline Workflow Debug Information ==="
          echo "Triggered by: ${{ github.event_name }}"
          echo "Branch: ${{ github.ref }}"
          echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
          echo "Pipeline Action: ${{ github.event.inputs.pipeline_action || 'run-complete-workflow' }}"
          echo "Deploy Cloud Run: ${{ github.event.inputs.deploy_cloud_run || 'true' }}"
          echo ""

  deploy-cloud-run:
    name: Deploy Cloud Run Services
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    if: |
      github.event.inputs.deploy_cloud_run == 'true' || 
      github.event.inputs.pipeline_action == 'deploy-cloud-run-only' ||
      github.event.inputs.pipeline_action == 'run-complete-workflow'
    
    outputs:
      vm_manager_url: ${{ steps.get-urls.outputs.vm_manager_url }}
      database_pipeline_url: ${{ steps.get-urls.outputs.database_pipeline_url }}
      orchestrator_url: ${{ steps.get-urls.outputs.orchestrator_url }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Build and Push Container Images
        run: |
          echo "Building Cloud Run container images..."

          # Build VM Manager
          docker build -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/pipeline-services/vm-manager:latest cloud-functions/vm-manager/
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/pipeline-services/vm-manager:latest

          # Build Database Pipeline
          docker build -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/pipeline-services/database-pipeline:latest cloud-functions/database-pipeline/
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/pipeline-services/database-pipeline:latest

          # Build Orchestrator
          docker build -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/pipeline-services/orchestrator:latest cloud-functions/orchestrator/
          docker push ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/pipeline-services/orchestrator:latest

      - name: Deploy Infrastructure with Terraform
        working-directory: ${{ env.TF_WORKING_DIR }}
        run: |
          # Create terraform.tfvars
          cat > terraform.tfvars << EOF
          project_id = "${{ env.PROJECT_ID }}"
          region     = "${{ env.REGION }}"
          zone       = "${{ env.ZONE }}"
          vm_name        = "cuba-buddy-data-pipeline-${{ github.event.inputs.environment || 'dev' }}"
          machine_type   = "e2-medium"
          disk_size      = 15
          environment    = "${{ github.event.inputs.environment || 'dev' }}"
          auto_delete_vm = true
          aws_hostname   = "${{ secrets.AWS_HOSTNAME }}"
          aws_user       = "forge"
          aws_private_key = <<-EOT
          ${{ secrets.AWS_PRIVATE_KEY }}
          EOT
          aws_public_key = <<-EOT
          ${{ secrets.AWS_PUBLIC_KEY }}
          EOT
          github_repo    = "**************:travel-buddies/gcp_data_pipeline_tools_ingestion.git"
          github_token   = "${{ secrets.GITHUB_TOKEN }}"
          pipeline_schedule = "0 2 * * 0"
          EOF
          
          # Initialize and apply Terraform
          terraform init
          terraform plan
          terraform apply -auto-approve

      - name: Get Cloud Run Service URLs
        id: get-urls
        run: |
          VM_MANAGER_URL=$(gcloud run services describe vm-manager --region=${{ env.REGION }} --format="value(status.url)")
          DATABASE_PIPELINE_URL=$(gcloud run services describe database-pipeline --region=${{ env.REGION }} --format="value(status.url)")
          ORCHESTRATOR_URL=$(gcloud run services describe pipeline-orchestrator --region=${{ env.REGION }} --format="value(status.url)")
          
          echo "vm_manager_url=$VM_MANAGER_URL" >> $GITHUB_OUTPUT
          echo "database_pipeline_url=$DATABASE_PIPELINE_URL" >> $GITHUB_OUTPUT
          echo "orchestrator_url=$ORCHESTRATOR_URL" >> $GITHUB_OUTPUT
          
          echo "=== Cloud Run Service URLs ==="
          echo "VM Manager: $VM_MANAGER_URL"
          echo "Database Pipeline: $DATABASE_PIPELINE_URL"
          echo "Orchestrator: $ORCHESTRATOR_URL"

      - name: Test Cloud Run Services
        run: |
          echo "Testing Cloud Run service health..."
          
          # Test VM Manager
          if curl -s "${{ steps.get-urls.outputs.vm_manager_url }}/health" | grep -q "healthy"; then
            echo "✓ VM Manager service is healthy"
          else
            echo "✗ VM Manager service health check failed"
          fi
          
          # Test Database Pipeline
          if curl -s "${{ steps.get-urls.outputs.database_pipeline_url }}/health" | grep -q "healthy"; then
            echo "✓ Database Pipeline service is healthy"
          else
            echo "✗ Database Pipeline service health check failed"
          fi
          
          # Test Orchestrator
          if curl -s "${{ steps.get-urls.outputs.orchestrator_url }}/health" | grep -q "healthy"; then
            echo "✓ Orchestrator service is healthy"
          else
            echo "✗ Orchestrator service health check failed"
          fi

  run-pipeline:
    name: Execute Database Pipeline
    runs-on: ubuntu-latest
    needs: deploy-cloud-run
    if: |
      github.event.inputs.pipeline_action == 'run-complete-workflow' ||
      github.event.inputs.pipeline_action == 'run-pipeline-only'
    
    steps:
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Execute Complete Pipeline Workflow
        run: |
          echo "=== Executing Complete Database Pipeline Workflow ==="
          
          ORCHESTRATOR_URL="${{ needs.deploy-cloud-run.outputs.orchestrator_url }}"
          
          # Prepare pipeline configuration
          PIPELINE_CONFIG=$(cat << EOF
          {
            "vm_name": "cuba-buddy-data-pipeline-${{ github.event.inputs.environment || 'dev' }}",
            "machine_type": "e2-medium",
            "disk_size": 15,
            "environment": "${{ github.event.inputs.environment || 'dev' }}",
            "aws_private_key": "${{ secrets.AWS_PRIVATE_KEY }}",
            "aws_public_key": "${{ secrets.AWS_PUBLIC_KEY }}",
            "aws_hostname": "${{ secrets.AWS_HOSTNAME }}"
          }
          EOF
          )
          
          echo "Calling orchestrator at: $ORCHESTRATOR_URL"
          
          # Execute the complete workflow
          RESPONSE=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d "$PIPELINE_CONFIG" \
            "$ORCHESTRATOR_URL/workflow/run")
          
          echo "Pipeline Response:"
          echo "$RESPONSE" | jq '.' || echo "$RESPONSE"
          
          # Check if pipeline was successful
          if echo "$RESPONSE" | jq -e '.status == "success"' > /dev/null; then
            echo "✓ Pipeline executed successfully!"
            VALIDATION_RESULT=$(echo "$RESPONSE" | jq -r '.validation_result // "unknown"')
            echo "DB Query output resulting in these rows: $VALIDATION_RESULT"
          else
            echo "✗ Pipeline execution failed"
            exit 1
          fi

  test-services:
    name: Test Services Only
    runs-on: ubuntu-latest
    if: github.event.inputs.pipeline_action == 'test-services-only'
    
    steps:
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Test Existing Cloud Run Services
        run: |
          echo "Testing existing Cloud Run services..."
          
          VM_MANAGER_URL=$(gcloud run services describe vm-manager --region=${{ env.GCP_REGION }} --format="value(status.url)" 2>/dev/null || echo "")
          DATABASE_PIPELINE_URL=$(gcloud run services describe database-pipeline --region=${{ env.GCP_REGION }} --format="value(status.url)" 2>/dev/null || echo "")
          ORCHESTRATOR_URL=$(gcloud run services describe pipeline-orchestrator --region=${{ env.GCP_REGION }} --format="value(status.url)" 2>/dev/null || echo "")
          
          if [ -n "$VM_MANAGER_URL" ]; then
            echo "Testing VM Manager at: $VM_MANAGER_URL"
            curl -s "$VM_MANAGER_URL/health" | jq '.' || echo "Health check failed"
          else
            echo "VM Manager service not found"
          fi
          
          if [ -n "$DATABASE_PIPELINE_URL" ]; then
            echo "Testing Database Pipeline at: $DATABASE_PIPELINE_URL"
            curl -s "$DATABASE_PIPELINE_URL/health" | jq '.' || echo "Health check failed"
          else
            echo "Database Pipeline service not found"
          fi
          
          if [ -n "$ORCHESTRATOR_URL" ]; then
            echo "Testing Orchestrator at: $ORCHESTRATOR_URL"
            curl -s "$ORCHESTRATOR_URL/health" | jq '.' || echo "Health check failed"
          else
            echo "Orchestrator service not found"
          fi
