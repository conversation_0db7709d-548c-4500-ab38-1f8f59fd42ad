# Production Environment Setup Guide

## 🚨 Important: Setting Up Production Environment in GitHub

Since you don't see the `prod` environment in your GitHub repository settings, you need to create it manually. Here's how:

## Step 1: Create Production Environment in GitHub

1. **Go to your GitHub repository**: `https://github.com/travel-buddies/gcp_data_pipeline_tools_ingestion`

2. **Navigate to Settings**:
   - Click on **Settings** tab (top of the repository)
   - In the left sidebar, click on **Environments**

3. **Create New Environment**:
   - Click **New environment** button
   - Enter environment name: `prod`
   - Click **Configure environment**

4. **Configure Environment Protection Rules** (Optional but Recommended):
   - ✅ **Required reviewers**: Add yourself or team members who should approve production deployments
   - ✅ **Wait timer**: Set a delay (e.g., 5 minutes) before deployment starts
   - ✅ **Deployment branches**: Restrict to `main` branch only

5. **Add Environment Secrets** (if different from repository secrets):
   - If your production environment uses different credentials, add them here
   - Otherwise, it will inherit from repository-level secrets

## Step 2: Verify Environment Configuration

After creating the `prod` environment, you should see:
- `dev` (if it exists)
- `prod` (newly created)
- `staging` (if it exists)

## Step 3: Test Production Environment

### Safe Testing Process:
```yaml
# Use these parameters for your first production test:
Branch: main
Environment: prod  # ← Now available in dropdown
Pipeline action: test-ssh-only  # ← Safe: Only tests SSH
```

### Expected Behavior:
- **VM Name**: `data-pipeline-prod-pipeline-vm`
- **Machine Type**: `e2-standard-4` (4 vCPUs, 16GB RAM)
- **Disk Size**: `16GB` (as per your request)
- **Same SSH target**: Still connects to your AWS EC2 at `***********`

## Step 4: Updated Disk Sizes

The following disk sizes are now configured:

### Development Environment:
- **Disk Size**: `10GB`
- **VM**: `e2-standard-2`
- **Cost**: ~$0.05/hour + $0.40/month storage

### Production Environment:
- **Disk Size**: `16GB`  
- **VM**: `e2-standard-4`
- **Cost**: ~$0.10/hour + $0.64/month storage

## Step 5: Environment-Specific Features

### Automatic Disk Size Selection:
The workflows now automatically select the correct disk size based on environment:
- `dev` → 10GB disk
- `prod` → 16GB disk
- `staging` → 10GB disk (default)

### Configuration Files Updated:
- ✅ `config/environments/dev/terraform.tfvars.template` → 10GB
- ✅ `config/environments/prod/terraform.tfvars.template` → 16GB
- ✅ `infrastructure/terraform/variables.tf` → 10GB default
- ✅ GitHub workflows updated with environment-specific logic

## Step 6: Testing Checklist

### Before Running Production:
- [ ] Production environment created in GitHub
- [ ] All required secrets are available (repository-level or environment-specific)
- [ ] Start with `test-ssh-only` action first
- [ ] Monitor GCP billing dashboard

### First Production Test:
```bash
# Expected workflow parameters:
Branch: main
Environment: prod
Pipeline action: test-ssh-only
```

### Expected Logs:
```
Creating VM: data-pipeline-prod-pipeline-vm
Machine Type: e2-standard-4
Disk Size: 16GB
SSH connection verified: [timestamp]
Creating target directory...
Directory created: /home/<USER>/cuba-buddy/De
Starting database dump...
Database dump successful! File size: [X] lines
```

## Troubleshooting

### If "prod" doesn't appear in environment dropdown:
1. Refresh the GitHub Actions page
2. Check if environment was created correctly in Settings → Environments
3. Ensure you have proper permissions to create environments

### If workflow fails with environment errors:
1. Check that all required secrets exist at repository level
2. Verify environment protection rules aren't blocking the run
3. Check workflow logs for specific error messages

## Cost Monitoring

### Expected Costs:
- **Dev**: ~$1.50/month (if running 1 hour/day)
- **Prod**: ~$3.00/month (if running 1 hour/day)
- **Storage**: Additional $0.40-$0.64/month per environment

### Monitoring:
- Check GCP Console → Billing
- Set up billing alerts for unexpected costs
- VMs auto-delete after pipeline completion (`auto_delete_vm = true`)
